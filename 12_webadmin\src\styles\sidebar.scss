//not
#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
    z-index: 1002;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 70px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: 24px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      font-weight: 500;
      padding: 0 28px!important;
      &:hover {
        background-color: $menuHover !important;
      }
      .icon{
        width: 26px;
        margin-right: 10px;
      }
    }
//#app .hideSidebar .el-submenu>.el-submenu__title .sub-el-icon
    .is-active>.el-submenu__title {
      //color: $subMenuActiveText !important;
    }
    & .nest-menu .el-menu-item{
      span{
        display: inline-block;
        height: 100%;
        width:100%;

        border-radius: 6px;
        padding-left: 0px;
        box-sizing: border-box;
        position: relative;
        &:hover {
          background-color: $subMenuHover;
        }
  &::after{
    content: '';
    display: inline-block;
    width: 3px;
  height: 3px;
  border-radius: 50%;
  background: $subMenuText;
  position: absolute;
  left: 17px;
  top: 50%;
  transform: translateY(-50%);
  }
      }
      &.is-active{
        span{
          background: $subMenuHover;
          &::after{
            background: $menuActiveText;
          }
        }
      }
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      height: 36px;
      line-height: 36px;
      font-weight: 400;
      padding: 0 28px 0 32px!important;
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
      color: $subMenuText!important;
      //&:hover {
      //  background-color: $subMenuHover !important;
      //}
    }
    .is-active{
      .el-menu-item.is-active {
        background-color: $menuHover !important;
        color: $menuActiveText!important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 76px !important;
    }

    .main-container {
      margin-left: 76px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;
text-align: center;
        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon,
        .icon {
          //margin-left: 19px;
          width: 20px;
          margin-right: 0;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
