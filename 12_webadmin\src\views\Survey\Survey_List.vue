<!--问卷列表页面-->
<template>
  <div class="grid-content survey">
    <div class="header">
      <div class="header-line1">
        <div class="search-input-box">
          <el-input
            v-model.trim="listQuery.SurveyNameLike"
            placeholder="请输入问卷名称搜索"
            size="medium"
            clearable
            @clear="handleFilter"
            class="search-input"
            @keyup.enter.native="handleFilter"
          />
          <img src="../../assets/images/sicon.png" class="sicon" v-if="!isSearching">
          <img src="../../assets/images/loading.png" class="loadingcon" v-else>
        </div>
        <el-button class="add-btn" type="primary" @click="handleAdd">+ 创建问卷</el-button>
      </div>
    </div>
    
    <div ref="list" class="tablelist" v-loading="listLoading">
      <div class="tablelist-title">问卷列表</div>
      <div class="tablelist-table">
        <el-table
          :data="list"
          style="width: 100%">
          <template slot="empty">
            <img src="../../assets/images/no-data.png" class="nodata">
            <div class="nodatatips">暂无数据</div>
          </template>
          
          <el-table-column
            prop="SurveyName"
            label="问卷名称"
            align='center'
            class-name="column-title"
            show-overflow-tooltip>
          </el-table-column>
          
          <el-table-column
            prop="SurveyDescription"
            label="问卷描述"
            align='center'
            class-name="column-desc"
            show-overflow-tooltip>
          </el-table-column>
          
          <el-table-column
            prop="ResponseCount"
            label="回复数"
            align='center'
            width="100"
            class-name="column-responses">
          </el-table-column>
          
          <el-table-column
            prop="AddTime"
            label="创建时间"
            align='center'
            class-name="column-time"
            show-overflow-tooltip>
            <template slot-scope="scope">
              {{ formatDate(scope.row.AddTime) }}
            </template>
          </el-table-column>
          
          <el-table-column
            label="状态"
            align='center'
            class-name="column-state">
            <template slot-scope="scope">
              <el-tag :type="scope.row.Status === 1 ? 'success' : 'info'" size="medium" effect="plain">
                {{ scope.row.Status === 1 ? '发布中' : '草稿' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" align='center' class-name="column-action">
            <template slot-scope="scope">
              <el-dropdown>
                <div class="flex-center operate">
                  操作
                </div>
                <el-dropdown-menu slot="dropdown" class="operate-dropdown">
                  <el-dropdown-item @click.native="handleEdit(scope.row)">
                    <span style="display:block;">编辑</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="handleDelete(scope.row)">
                    <span style="display:block;">删除</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="viewResponses(scope.row)">
                    <span style="display:block;">查看回复</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="viewAnalysis(scope.row)">
                    <span style="display:block;">数据分析</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="previewSurvey(scope.row)">
                    <span style="display:block;">预览</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.Status === 0" @click.native="publishSurvey(scope.row)">
                    <span style="display:block;">发布</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-else @click.native="unpublishSurvey(scope.row)">
                    <span style="display:block;">下线</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div class="pagination-box">
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.PageIndex"
          :limit.sync="listQuery.PageSize"
          @pagination="getList"
        />
      </div>
    </div>
    
    <!-- 问卷预览对话框 -->
    <el-dialog 
      :visible.sync="previewVisible"
      title="问卷预览"
      width="800px"
      custom-class="preview-dialog"
      :close-on-click-modal="false"
      append-to-body>
      <div v-if="currentSurvey" class="survey-preview">
        <h3>{{ currentSurvey.SurveyName }}</h3>
        <p class="survey-desc">{{ currentSurvey.SurveyDescription }}</p>
        
        <div class="question-list">
          <div v-for="(question, index) in currentSurvey.Questions" :key="index" class="question-item">
            <div class="question-header">
              <span class="question-number">{{ index + 1 }}</span>
              <span class="question-title">{{ question.QuestionText }}</span>
              <span class="question-required" v-if="question.Required">*</span>
              <span class="question-type">{{ getQuestionTypeLabel(question.QuestionType) }}</span>
            </div>
            
            <div class="question-options" v-if="['radio', 'checkbox', 'select'].includes(question.QuestionType)">
              <div v-for="(option, optIndex) in question.Options" :key="optIndex" class="option-item">
                <template v-if="question.QuestionType === 'radio'">
                  <el-radio :label="option.OptionValue" disabled>{{ option.OptionText }}</el-radio>
                </template>
                <template v-else-if="question.QuestionType === 'checkbox'">
                  <el-checkbox :label="option.OptionValue" disabled>{{ option.OptionText }}</el-checkbox>
                </template>
                <template v-else-if="question.QuestionType === 'select'">
                  <div class="select-option">{{ option.OptionText }}</div>
                </template>
              </div>
            </div>
            
            <div class="question-input" v-else-if="question.QuestionType === 'text'">
              <el-input type="text" placeholder="请输入..." disabled />
            </div>
            
            <div class="question-input" v-else-if="question.QuestionType === 'textarea'">
              <el-input type="textarea" :rows="3" placeholder="请输入..." disabled />
            </div>
            
            <div class="question-input" v-else-if="question.QuestionType === 'date'">
              <el-date-picker type="date" placeholder="选择日期" disabled style="width: 100%" />
            </div>
            
            <div class="question-input" v-else-if="question.QuestionType === 'rate'">
              <el-rate disabled />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import controller from '@/controllers/Survey/Survey_List'
export default controller
</script>

<style lang="scss">
.preview-dialog{
  .el-dialog__header{
    padding: 22px 32px;
  }
  .el-dialog__body{
    padding: 23px 32px 32px;
    
    .survey-preview {
      h3 {
        font-size: 20px;
        text-align: center;
        margin-bottom: 15px;
      }
      
      .survey-desc {
        color: #666;
        text-align: center;
        margin-bottom: 30px;
      }
      
      .question-list {
        .question-item {
          border: 1px solid #eee;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
          background-color: #fafafa;
          
          .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            
            .question-number {
              background: #409EFF;
              color: white;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;
              font-size: 14px;
            }
            
            .question-title {
              font-size: 16px;
              font-weight: 500;
              flex: 1;
            }
            
            .question-required {
              color: #F56C6C;
              margin-left: 5px;
            }
            
            .question-type {
              color: #909399;
              font-size: 12px;
              margin-left: 10px;
              background: #f0f0f0;
              padding: 2px 6px;
              border-radius: 4px;
            }
          }
          
          .question-options {
            padding-left: 15px;
            
            .option-item {
              margin-bottom: 10px;
            }
            
            .select-option {
              padding: 8px 10px;
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              margin-bottom: 8px;
              color: #606266;
            }
          }
          
          .question-input {
            padding-left: 15px;
          }
        }
      }
    }
  }
}
</style>

<style scoped lang="scss">
.grid-content{
  margin: 0 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header{
  height: 84px;
  background: #FFFFFF;
  border-radius: 6px;
  padding: 20px 30px;
  
  .header-line1{
    height: 100%;
    display: flex;
    justify-content: space-between;
    
    .el-input{
      margin: 0;
    }
    
    .search-input-box {
      position: relative;
      
      .sicon, .loadingcon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
      }
      
      .loadingcon {
        animation: spin 1s linear infinite;
      }
    }
    
    .add-btn {
      height: 40px;
      padding: 0 20px;
    }
  }
}

.tablelist{
  position: relative;
  margin-top: 20px;
  background: #FFFFFF;
  border-radius: 6px;
  padding: 30px;
  flex: 1;
  
  .tablelist-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
  }
  
  .operate{
    width: 75px;
    height: 28px;
    background: #F9F9F9;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #7E8299;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.pagination-box{
  position: absolute;
  bottom: 32px;
  right: 15px;
}

.nodata{
  width: 80px;
  height: 80px;
}

.nodatatips{
  margin-top: 10px;
  color: #999;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}
</style>
