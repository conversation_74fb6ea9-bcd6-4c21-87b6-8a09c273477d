//not
/* Layout */
import Layout from '@/layout'
const commonMenuRouter = [
  {
    id: '800',
    path: '/Activity',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'Activity',
    meta: {
      title: '活动管理',
      icon: require('@/assets/menu/menu2.png') // Activity management - event/activity icon
    },
    children: [
      {
        id: '800001',
        path: 'ActiveInfoList',
        component: () => import('@/views/Activity/Active_Info_list'),
        name: 'ActiveInfoList',
        meta: {
          title: '活动列表',
          icon: 'el-icon-trophy'
        }
      },
      {
        id: '800002',
        path: 'AddActivity',
        hidden: true,
        component: () => import('@/views/Activity/Add_Activity'),
        name: 'AddActivity',
        meta: {
          title: '添加活动',
          pre: {
            path: '/Activity/ActiveInfoList',
            meta: { title: '活动列表' }
          }
        }
      }
    ]
  },
  {
    id: '100',
    path: '/WeChat',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'WeChat',
    meta: {
      title: '微信内容管理',
      icon: require('@/assets/menu/menu1.png') // WeChat content management - communication icon
    },
    children: [
      {
        id: '100000',
        path: 'MPDraftlist',
        component: () => import('@/views/WeChat/Drafts/MP_Draft_list'),
        name: 'MPDraftlist',
        meta: {
          title: '草稿箱',
          icon: 'el-icon-edit-outline'
        }
      },
      {
        id: '100310',
        path: 'AddImageText',
        hidden: true,
        component: () => import('@/views/WeChat/Drafts/AddImageText'),
        name: 'AddImageText',
        meta: {
          title: '新建图文',
          pre: {
            path: '/WeChat/MPDraftlist',
            meta: { title: '草稿箱' }
          }
        }
      },
      {
        id: '100100',
        path: 'Material_list',
        component: () => import('@/views/WeChat/Material/Material_list'),
        name: 'Material_list',
        meta: {
          title: '素材管理',
          icon: 'el-icon-folder-opened'
        }
      },
      {
        id: '100110',
        path: 'AddImg',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddImg'),
        name: 'AddImg',
        meta: {
          title: '上传图片',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100120',
        path: 'AddAudio',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddAudio'),
        name: 'AddAudio',
        meta: {
          title: '上传音频',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100130',
        path: 'AddVideo',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddVideo'),
        name: 'AddVideo',
        meta: {
          title: '上传视频',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100120',
        path: 'Graphic_Push_list',
        component: () => import('@/views/WeChat/GraphicPush/Graphic_Push_list'),
        name: 'Graphic_Push_list',
        meta: {
          title: '发布记录',
          icon: 'el-icon-s-order'
        }
      },
     
      //客户管理
      {
        id: '100140',
        path: 'Client_Management',
        component: () => import('@/views/WeChat/ClientManagement/Client_Management'),
        name: 'Client_Management_List',
        meta: {
          title: '用户管理',
          icon: 'el-icon-user-solid'
        }
      },
      {
        id: '100150',
        path: 'Menu_list',
        component: () => import('@/views/WeChat/CustomMenu/Menu_list'),
        name: 'Menu_list',
        meta: {
          title: '自定义菜单',
          icon: 'el-icon-menu'
        }
      },
      {
        id: '100160',
        path: 'Add_Menu',
        hidden: true,
        component: () => import('@/views/WeChat/CustomMenu/Add_Menu'),
        name: 'Add_Menu',
        meta: {
          title: '新建菜单',
          pre: {
            path: '/WeChat/Menu_list',
            meta: { title: '自定义菜单' }
          }
        }
      },
      {
        id: '100170',
        path: 'Reply_list',
        component: () => import('@/views/WeChat/AutoReply/Reply_list'),
        name: 'Reply_list',
        meta: {
          title: '自动回复',
          icon: 'el-icon-chat-dot-round'
        }
      },
      {
        id: '100180',
        path: 'Add_Reply',
        hidden: true,
        component: () => import('@/views/WeChat/AutoReply/Add_Reply'),
        name: 'Add_Reply',
        meta: {
          title: '添加回复',
          pre: {
            path: '/WeChat/Reply_list',
            meta: { title: '自动回复' }
          }
        }
      },
      {
        id: '100190',
        path: 'Template_list',
        component: () => import('@/views/WeChat/TemplateMessage/Template_list'),
        name: 'Template_list',
        meta: {
          title: '模板消息',
          icon: 'el-icon-message-solid'
        }
      },
      // {
      //   id: '100191',
      //   path: 'TemplatesubscriptionInfo_list',
      //   component: () => import('@/views/WeChat/TemplatesubscriptionMessage/TemplatesubscriptionInfo_list'),
      //   name: 'TemplatesubscriptionInfo_list',
      //   meta: {
      //     title: '订阅消息'
      //   },
      // },
      // {
      //   id: '100192',
      //   path: 'Add_TemplatesubscriptionInfo',
      //   hidden: true,
      //   component: () => import('@/views/WeChat/TemplatesubscriptionMessage/Add_TemplatesubscriptionInfo'),
      //   name: 'Add_TemplatesubscriptionInfo',
      //   meta: {
      //     title: '添加订阅消息'
      //   },
      // },
      {
        id: '100200',
        path: 'Add_Template',
        hidden: true,
        component: () => import('@/views/WeChat/TemplateMessage/Add_Template'),
        name: 'Add_Template',
        meta: {
          title: '添加模板',
          pre: {
            path: '/WeChat/Template_list',
            meta: { title: '模板消息' }
          }
        }
      },
      {
        id: '100210',
        path: 'Qr_list',
        component: () => import('@/views/WeChat/QrCode/Qr_list'),
        name: 'Qr_list',
        meta: {
          title: '二维码管理',
          icon: 'el-icon-picture-outline'
        }
      },
      {
        id: '100220',
        path: 'Add_Qr',
        hidden: true,
        component: () => import('@/views/WeChat/QrCode/Add_Qr'),
        name: 'Add_Qr',
        meta: {
          title: '添加二维码',
          pre: {
            path: '/WeChat/Qr_list',
            meta: { title: '二维码管理' }
          }
        }
      }
      //{
      //  path: 'Material',
      //  component: () => import('@/views/WeChat/Index'),
      //  redirect: '/WeChat/Material/Material_list',
      //  name: 'Material',
      //  meta: {
      //    //isPage: true,
      //    title: '素材管理'
      //  },
      //  children: [
      //    {
      //      path: 'Material_list',
      //      component: () => import('@/views/WeChat/Material_list'),
      //      meta: {
      //        title: '素材列表'
      //      },
      //    },
      //    {
      //      path: 'Add_Material',
      //      name: 'Add_Material',
      //      hidden: true,
      //      component: () => import('@/views/WeChat/Add_Material'),
      //      meta: {
      //        title: '新建素材'
      //      },
      //    },
      //  ]
      //},
    ]
  },
  //{
  //  path: '/Admin',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Admin',
  //  meta: {
  //    title: '管理端1',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Userlist',
  //      component: () => import('@/views/Admin/tUserInfo_list'),
  //      name: 'Userlist',
  //      meta: {
  //        title: '用户信息'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Article',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Article',
  //  meta: {
  //    title: 'Article',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'ArticleCataloglist',
  //      component: () => import('@/views/Article/Article_Catalog_list'),
  //      name: 'ArticleCataloglist',
  //      meta: {
  //        title: '素材'
  //      }
  //    },
  //    {
  //      path: 'Articlelist',
  //      component: () => import('@/views/Article/Article_Info_list'),
  //      name: 'Articlelist',
  //      meta: {
  //        title: '文章'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Organize',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Organize',
  //  meta: {
  //    title: '组织结构',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Companylist',
  //      component: () => import('@/views/Organize/tCompanyInfo_list'),
  //      name: 'Companylist',
  //      meta: {
  //        title: '公司信息'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Platform',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Platform',
  //  meta: {
  //    title: 'Platform',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Platformlist',
  //      component: () => import('@/views/Platform/tPlatformInfo_list'),
  //      name: 'Platformlist',
  //      meta: {
  //        title: 'tPlatformInfo'
  //      }
  //    },
  //    {
  //      path: 'PlatformUserlist',
  //      component: () => import('@/views/Platform/tPlatformUser_list'),
  //      name: 'PlatformUserlist',
  //      meta: {
  //        title: 'tPlatformUser'
  //      }
  //    },
  //  ]
  //},
    {
        id: '200',
        path: '/Survey',
        component: Layout,
        redirect: 'noRedirect',
        alwaysShow: true,
        name: 'Survey',
        meta: {
            title: '数据收集',
            icon: require('@/assets/menu/menu2.png'), // Survey management - questionnaire icon
        },
        children: [
  //         //{
  //         //  path: 'SurveyAnswerlist',
  //         //  component: () => import('@/views/Survey/Survey_Answer_list'),
  //         //  name: 'SurveyAnswerlist',
  //         //  meta: {
  //         //    title: 'Survey_Answer'
  //         //  }
  //         //},
  //         //{
  //         //  path: 'SurveyAnswerLoglist',
  //         //  component: () => import('@/views/Survey/Survey_AnswerLog_list'),
  //         //  name: 'SurveyAnswerLoglist',
  //         //  meta: {
  //         //    title: 'AnswerLog'
  //         //  }
  //         //},
            {
                id: '200110',
                path: 'Surveylist',
                component: () => import('@/views/Survey/Survey_Info_list'),
                name: 'Surveylist',
                meta: {
                    title: '问卷调查',
                    icon: 'el-icon-document-checked',
                },
            },
            {
                id: '200120',
                path: 'AddSurvey',
                hidden: true,
                component: () => import('@/views/Survey/Add_Survey'),
                name: 'AddSurvey',
                meta: {
                    title: ' 添加问卷',
                    pre: {
                        path: '/Survey/Surveylist',
                        meta: { title: '问卷调查' },
                    },
                },
            },
            {
                id: '200130',
                path: 'PreviewSurvey',
                hidden: true,
                component: () => import('@/views/Survey/PreviewSurvey'),
                name: 'PreviewSurvey',
                meta: {
                    title: ' 问卷预览',
                },
            },
  //         //{
  //         //  path: 'SurveyQuestionlist',
  //         //  component: () => import('@/views/Survey/Survey_Question_list'),
  //         //  name: 'SurveyQuestionlist',
  //         //  meta: {
  //         //    title: 'Survey_Question'
  //         //  }
  //         //},
        ],
    },

  {
    id: '300100',
    path: '/Report',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'Report',
    meta: {
      title: '统计分析',
      icon: require('@/assets/menu/menu3.png') // Statistical analysis - chart/analytics icon
    },
    children: [
      {
        id: '300110',
        path: 'qr',
        component: () => import('@/views/Report/Qr'),
        name: 'qr',
        meta: {
                    title: '扫码分析',
                    icon: 'el-icon-data-analysis',
                },
      },
      // {
      //   id: '300130',
      //   path: 'survey',
      //   component: () => import('@/views/Report/Survey'),
      //   hidden: true,
      //   name: 'survey',
      //   meta: {
      //     title: '问卷分析'
      //   }
      // },
      // {
      //   id: '300131',
      //   path: 'SurveyDetail',
      //   hidden: true,
      //   component: () => import('@/views/Report/SurveyDetail'),
      //   name: 'SurveyDetail',
      //   meta: {
      //     title: '问卷分析',
      //     pre: {
      //       path: '/Report/Survey',
      //       meta: { title: '问卷分析' }
      //     }
      //   }
      // },
      {
        id: '300111',
        path: 'qrdetail',
        hidden: true,
        component: () => import('@/views/Report/QrDetail'),
        name: 'qrdetail',
        meta: {
          title: '扫码分析',
          pre: {
            path: '/Report/Qr',
            meta: { title: '扫码分析' }
          }
        }
      }
    ]
  },
  {
    meta: {
      title: '系统管理',
      icon: require('@/assets/menu/menu4.png') // System management - settings/admin icon
    },

    path: '/SysManage',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'SysManage',
    // redirect:'/SysManage/UserManage',
    children: [
      {
        path: 'UserManage',
        component: () => import('@/views/SysManage/UserManage'),
        name: 'UserManage',
        meta: {
          title: '用户管理',
          icon: 'el-icon-user'
        }
      },
      {
        path: 'RoleList',
        name: 'RoleList',
        component: () => import('@/views/SysManage/RoleList'),
        meta: {
          title: '角色列表',
          icon: 'el-icon-s-custom'
        }
      },
      {
        path: 'AddRole',
        component: () => import('@/views/SysManage/AddRole'),
        name: 'AddRole',
        hidden: true,
        meta: {
          title: '角色管理',
          pre: {
            path: '/SysManage/RoleList',
            meta: { title: '角色列表' }
          }
        }
      },
      {
        path: '/sysconfig',
        name: 'sysconfig',
        hidden: false,
        component: () => import('@/views/User/sysconfig'),
        meta: { title: '系统配置', icon: 'el-icon-setting', noCache: true }
      },
      {
        path: '/wechatconfig',
        name: 'wechatconfig',
        hidden: false,
        component: () => import('@/views/User/wechatconfig'),
        meta: { title: '微信配置', icon: 'el-icon-chat-line-round', noCache: true }
      }
    ]
  },
  
]
export default commonMenuRouter
