//not
/* Layout */
import Layout from '@/layout'
const commonMenuRouter = [
  {
    id: '100',
    path: '/WeChat',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'WeChat',
    meta: {
      title: '微信内容管理',
      icon: require('@/assets/menu/menu1.png')
    },
    children: [
      {
        id: '100000',
        path: 'MPDraftlist',
        component: () => import('@/views/WeChat/Drafts/MP_Draft_list'),
        name: 'MPDraftlist',
        meta: {
          title: '草稿箱'
        }
      },
      {
        id: '100310',
        path: 'AddImageText',
        hidden: true,
        component: () => import('@/views/WeChat/Drafts/AddImageText'),
        name: 'AddImageText',
        meta: {
          title: '新建图文',
          pre: {
            path: '/WeChat/MPDraftlist',
            meta: { title: '草稿箱' }
          }
        }
      },
      {
        id: '100100',
        path: 'Material_list',
        component: () => import('@/views/WeChat/Material/Material_list'),
        name: 'Material_list',
        meta: {
          title: '素材管理'
        }
      },
      {
        id: '100110',
        path: 'AddImg',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddImg'),
        name: 'AddImg',
        meta: {
          title: '上传图片',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100120',
        path: 'AddAudio',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddAudio'),
        name: 'AddAudio',
        meta: {
          title: '上传音频',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100130',
        path: 'AddVideo',
        hidden: true,
        component: () => import('@/views/WeChat/Material/AddVideo'),
        name: 'AddVideo',
        meta: {
          title: '上传视频',
          pre: {
            path: '/WeChat/Material_list',
            meta: { title: '素材管理' }
          }
        }
      },
      {
        id: '100120',
        path: 'Graphic_Push_list',
        component: () => import('@/views/WeChat/GraphicPush/Graphic_Push_list'),
        name: 'Graphic_Push_list',
        meta: {
          title: '发布记录'
        }
      },
      // {
      //   id: '100130',
      //   path: 'Add_Graphic_Push',
      //   hidden: true,
      //   component: () => import('@/views/WeChat/GraphicPush/Add_Graphic_Push'),
      //   name: 'Add_Graphic_Push',
      //   meta: {
      //     title: '新建推送',
      //     pre: {
      //       path: '/WeChat/Graphic_Push_list',
      //       meta: {title: '发布记录'}
      //     }
      //   },
      // },

      // {
      //   id: '100020',
      //   path: 'MPFreepublishlist',
      //   component: () => import('@/views/WeMP/MP_Freepublish_list'),
      //   name: 'MPFreepublishlist',
      //   meta: {
      //     title: '发表记录'
      //   }
      // },
      //客户管理
      {
        id: '100140',
        path: 'Client_Management',
        component: () => import('@/views/WeChat/ClientManagement/Client_Management'),
        name: 'Client_Management_List',
        meta: {
          title: '用户管理'
        }
      },
      {
        id: '100150',
        path: 'Menu_list',
        component: () => import('@/views/WeChat/CustomMenu/Menu_list'),
        name: 'Menu_list',
        meta: {
          title: '自定义菜单'
        }
      },
      {
        id: '100160',
        path: 'Add_Menu',
        hidden: true,
        component: () => import('@/views/WeChat/CustomMenu/Add_Menu'),
        name: 'Add_Menu',
        meta: {
          title: '新建菜单',
          pre: {
            path: '/WeChat/Menu_list',
            meta: { title: '自定义菜单' }
          }
        }
      },
      {
        id: '100170',
        path: 'Reply_list',
        component: () => import('@/views/WeChat/AutoReply/Reply_list'),
        name: 'Reply_list',
        meta: {
          title: '自动回复'
        }
      },
      {
        id: '100180',
        path: 'Add_Reply',
        hidden: true,
        component: () => import('@/views/WeChat/AutoReply/Add_Reply'),
        name: 'Add_Reply',
        meta: {
          title: '添加回复',
          pre: {
            path: '/WeChat/Reply_list',
            meta: { title: '自动回复' }
          }
        }
      },
      {
        id: '100190',
        path: 'Template_list',
        component: () => import('@/views/WeChat/TemplateMessage/Template_list'),
        name: 'Template_list',
        meta: {
          title: '模板消息'
        }
      },
      // {
      //   id: '100191',
      //   path: 'TemplatesubscriptionInfo_list',
      //   component: () => import('@/views/WeChat/TemplatesubscriptionMessage/TemplatesubscriptionInfo_list'),
      //   name: 'TemplatesubscriptionInfo_list',
      //   meta: {
      //     title: '订阅消息'
      //   },
      // },
      // {
      //   id: '100192',
      //   path: 'Add_TemplatesubscriptionInfo',
      //   hidden: true,
      //   component: () => import('@/views/WeChat/TemplatesubscriptionMessage/Add_TemplatesubscriptionInfo'),
      //   name: 'Add_TemplatesubscriptionInfo',
      //   meta: {
      //     title: '添加订阅消息'
      //   },
      // },
      {
        id: '100200',
        path: 'Add_Template',
        hidden: true,
        component: () => import('@/views/WeChat/TemplateMessage/Add_Template'),
        name: 'Add_Template',
        meta: {
          title: '添加模板',
          pre: {
            path: '/WeChat/Template_list',
            meta: { title: '模板消息' }
          }
        }
      },
      {
        id: '100210',
        path: 'Qr_list',
        component: () => import('@/views/WeChat/QrCode/Qr_list'),
        name: 'Qr_list',
        meta: {
          title: '二维码管理'
        }
      },
      {
        id: '100220',
        path: 'Add_Qr',
        hidden: true,
        component: () => import('@/views/WeChat/QrCode/Add_Qr'),
        name: 'Add_Qr',
        meta: {
          title: '添加二维码',
          pre: {
            path: '/WeChat/Qr_list',
            meta: { title: '二维码管理' }
          }
        }
      }
      //{
      //  path: 'Material',
      //  component: () => import('@/views/WeChat/Index'),
      //  redirect: '/WeChat/Material/Material_list',
      //  name: 'Material',
      //  meta: {
      //    //isPage: true,
      //    title: '素材管理'
      //  },
      //  children: [
      //    {
      //      path: 'Material_list',
      //      component: () => import('@/views/WeChat/Material_list'),
      //      meta: {
      //        title: '素材列表'
      //      },
      //    },
      //    {
      //      path: 'Add_Material',
      //      name: 'Add_Material',
      //      hidden: true,
      //      component: () => import('@/views/WeChat/Add_Material'),
      //      meta: {
      //        title: '新建素材'
      //      },
      //    },
      //  ]
      //},
    ]
  },
  //{
  //  path: '/Admin',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Admin',
  //  meta: {
  //    title: '管理端1',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Userlist',
  //      component: () => import('@/views/Admin/tUserInfo_list'),
  //      name: 'Userlist',
  //      meta: {
  //        title: '用户信息'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Article',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Article',
  //  meta: {
  //    title: 'Article',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'ArticleCataloglist',
  //      component: () => import('@/views/Article/Article_Catalog_list'),
  //      name: 'ArticleCataloglist',
  //      meta: {
  //        title: '素材'
  //      }
  //    },
  //    {
  //      path: 'Articlelist',
  //      component: () => import('@/views/Article/Article_Info_list'),
  //      name: 'Articlelist',
  //      meta: {
  //        title: '文章'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Organize',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Organize',
  //  meta: {
  //    title: '组织结构',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Companylist',
  //      component: () => import('@/views/Organize/tCompanyInfo_list'),
  //      name: 'Companylist',
  //      meta: {
  //        title: '公司信息'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/Platform',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'Platform',
  //  meta: {
  //    title: 'Platform',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Platformlist',
  //      component: () => import('@/views/Platform/tPlatformInfo_list'),
  //      name: 'Platformlist',
  //      meta: {
  //        title: 'tPlatformInfo'
  //      }
  //    },
  //    {
  //      path: 'PlatformUserlist',
  //      component: () => import('@/views/Platform/tPlatformUser_list'),
  //      name: 'PlatformUserlist',
  //      meta: {
  //        title: 'tPlatformUser'
  //      }
  //    },
  //  ]
  //},
    {
        id: '200',
        path: '/Survey',
        component: Layout,
        redirect: 'noRedirect',
        alwaysShow: true,
        name: 'Survey',
        meta: {
            title: 'Survey',
            icon: require('@/assets/menu/menu2.png'),
        },
        children: [
  //         //{
  //         //  path: 'SurveyAnswerlist',
  //         //  component: () => import('@/views/Survey/Survey_Answer_list'),
  //         //  name: 'SurveyAnswerlist',
  //         //  meta: {
  //         //    title: 'Survey_Answer'
  //         //  }
  //         //},
  //         //{
  //         //  path: 'SurveyAnswerLoglist',
  //         //  component: () => import('@/views/Survey/Survey_AnswerLog_list'),
  //         //  name: 'SurveyAnswerLoglist',
  //         //  meta: {
  //         //    title: 'AnswerLog'
  //         //  }
  //         //},
            {
                id: '200110',
                path: 'Surveylist',
                component: () => import('@/views/Survey/Survey_Info_list'),
                name: 'Surveylist',
                meta: {
                    title: '问卷调查',
                },
            },
            {
                id: '200120',
                path: 'AddSurvey',
                hidden: true,
                component: () => import('@/views/Survey/Add_Survey'),
                name: 'AddSurvey',
                meta: {
                    title: ' 添加问卷',
                    pre: {
                        path: '/Survey/Surveylist',
                        meta: { title: '问卷调查' },
                    },
                },
            },
            {
                id: '200130',
                path: 'PreviewSurvey',
                hidden: true,
                component: () => import('@/views/Survey/PreviewSurvey'),
                name: 'PreviewSurvey',
                meta: {
                    title: ' 问卷预览',
                },
            },
  //         //{
  //         //  path: 'SurveyQuestionlist',
  //         //  component: () => import('@/views/Survey/Survey_Question_list'),
  //         //  name: 'SurveyQuestionlist',
  //         //  meta: {
  //         //    title: 'Survey_Question'
  //         //  }
  //         //},
        ],
    },

  {
    id: '300100',
    path: '/Report',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'Report',
    meta: {
      title: '统计分析',
      icon: require('@/assets/menu/menu3.png')
    },
    children: [
      {
        id: '300110',
        path: 'qr',
        component: () => import('@/views/Report/Qr'),
        name: 'qr',
        meta: {
                    title: '扫码分析',
                },
      },
      // {
      //   id: '300130',
      //   path: 'survey',
      //   component: () => import('@/views/Report/Survey'),
      //   hidden: true,
      //   name: 'survey',
      //   meta: {
      //     title: '问卷分析'
      //   }
      // },
      // {
      //   id: '300131',
      //   path: 'SurveyDetail',
      //   hidden: true,
      //   component: () => import('@/views/Report/SurveyDetail'),
      //   name: 'SurveyDetail',
      //   meta: {
      //     title: '问卷分析',
      //     pre: {
      //       path: '/Report/Survey',
      //       meta: { title: '问卷分析' }
      //     }
      //   }
      // },
      {
        id: '300111',
        path: 'qrdetail',
        hidden: true,
        component: () => import('@/views/Report/QrDetail'),
        name: 'qrdetail',
        meta: {
          title: '扫码分析',
          pre: {
            path: '/Report/Qr',
            meta: { title: '扫码分析' }
          }
        }
      }
    ]
  },
  {
    meta: {
      title: '系统管理',
      icon: require('@/assets/menu/menu1.png')
    },

    path: '/SysManage',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'SysManage',
    // redirect:'/SysManage/UserManage',
    children: [
      {
        path: 'UserManage',
        component: () => import('@/views/SysManage/UserManage'),
        name: 'UserManage',
        meta: {
          title: '用户管理'
        }
      },
      {
        path: 'RoleList',
        name: 'RoleList',
        component: () => import('@/views/SysManage/RoleList'),
        meta: {
          title: '角色列表'
        }
      },
      {
        path: 'AddRole',
        component: () => import('@/views/SysManage/AddRole'),
        name: 'AddRole',
        hidden: true,
        meta: {
          title: '角色管理',
          pre: {
            path: '/SysManage/RoleList',
            meta: { title: '角色列表' }
          }
        }
      },
      {
        path: '/sysconfig',
        name: 'sysconfig',
        hidden: false,
        component: () => import('@/views/User/sysconfig'),
        meta: { title: '系统配置', icon: 'user', noCache: true }
      },
      {
        path: '/wechatconfig',
        name: 'wechatconfig',
        hidden: false,
        component: () => import('@/views/User/wechatconfig'),
        meta: { title: '微信配置', icon: 'user', noCache: true }
      }
    ]
  },
  //{
  //  path: '/ThirdOpen',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'ThirdOpen',
  //  meta: {
  //    title: 'ThirdOpen',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'OpenOAuthConfiglist',
  //      component: () => import('@/views/ThirdOpen/Open_OAuthConfig_list'),
  //      name: 'OpenOAuthConfiglist',
  //      meta: {
  //        title: 'tOAuthConfig'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/WechatConfig',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'WechatConfig',
  //  meta: {
  //    title: 'WeCommon',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'Wechatlist',
  //      component: () => import('@/views/WechatConfig/Wechat_Info_list'),
  //      name: 'Wechatlist',
  //      meta: {
  //        title: 'tWechatInfo'
  //      }
  //    },
  //    {
  //      path: 'WechatManagerRellist',
  //      component: () => import('@/views/WechatConfig/Wechat_Manager_Rel_list'),
  //      name: 'WechatManagerRellist',
  //      meta: {
  //        title: 'rel_Manager_Wechat'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/WeMP',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'WeMP',
  //  meta: {
  //    title: '公众号',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'MPActionInterFacelist',
  //      component: () => import('@/views/WeMP/MP_ActionInterFace_list'),
  //      name: 'MPActionInterFacelist',
  //      meta: {
  //        title: 'tActionInterFace'
  //      }
  //    },
  //    {
  //      path: 'MPActionRecordlist',
  //      component: () => import('@/views/WeMP/MP_Action_Record_list'),
  //      name: 'MPActionRecordlist',
  //      meta: {
  //        title: '操作记录'
  //      }
  //    },
  //    {
  //      path: 'MPArticleImagelist',
  //      component: () => import('@/views/WeMP/MP_ArticleImage_list'),
  //      name: 'MPArticleImagelist',
  //      meta: {
  //        title: 'tArticleImage'
  //      }
  //    },
  //    {
  //      path: 'MPCustomerMsgRecordlist',
  //      component: () => import('@/views/WeMP/MP_CustomerMsg_Record_list'),
  //      name: 'MPCustomerMsgRecordlist',
  //      meta: {
  //        title: '客服消息'
  //      }
  //    },
  //    {
  //      path: 'MPImageGrouplist',
  //      component: () => import('@/views/WeMP/MP_ImageGroup_list'),
  //      name: 'MPImageGrouplist',
  //      meta: {
  //        title: 'tImageGroup'
  //      }
  //    },
  //    {
  //      path: 'MPMasterlist',
  //      component: () => import('@/views/WeMP/MP_Master_list'),
  //      name: 'MPMasterlist',
  //      meta: {
  //        title: 'tWechatMaster'
  //      }
  //    },
  //    {
  //      path: 'MPMasterChildlist',
  //      component: () => import('@/views/WeMP/MP_Master_Child_list'),
  //      name: 'MPMasterChildlist',
  //      meta: {
  //        title: 'tChildMaster'
  //      }
  //    },
  //    {
  //      path: 'MPMediaMateriallist',
  //      component: () => import('@/views/WeMP/MP_MediaMaterial_list'),
  //      name: 'MPMediaMateriallist',
  //      meta: {
  //        title: '素材'
  //      }
  //    },
  //    {
  //      path: 'MPMenulist',
  //      component: () => import('@/views/WeMP/MP_Menu_list'),
  //      name: 'MPMenulist',
  //      meta: {
  //        title: '菜单'
  //      }
  //    },
  //    {
  //      path: 'MPMenuUserTaglist',
  //      component: () => import('@/views/WeMP/MP_Menu_UserTag_list'),
  //      name: 'MPMenuUserTaglist',
  //      meta: {
  //        title: '个性化菜单'
  //      }
  //    },
  //    {
  //      path: 'MPMsgLoglist',
  //      component: () => import('@/views/WeMP/MP_MsgLog_list'),
  //      name: 'MPMsgLoglist',
  //      meta: {
  //        title: 'tWechatMsgLog'
  //      }
  //    },
  //    {
  //      path: 'MPPreviewUserRellist',
  //      component: () => import('@/views/WeMP/MP_PreviewUser_Rel_list'),
  //      name: 'MPPreviewUserRellist',
  //      meta: {
  //        title: 'tPreviewUser'
  //      }
  //    },
  //    {
  //      path: 'MPPushLoglist',
  //      component: () => import('@/views/WeMP/MP_PushLog_list'),
  //      name: 'MPPushLoglist',
  //      meta: {
  //        title: 'tPushLog'
  //      }
  //    },
  //    {
  //      path: 'MPPushRulelist',
  //      component: () => import('@/views/WeMP/MP_PushRule_list'),
  //      name: 'MPPushRulelist',
  //      meta: {
  //        title: 'tPushRule'
  //      }
  //    },
  //    {
  //      path: 'MPQRCodelist',
  //      component: () => import('@/views/WeMP/MP_QR_Code_list'),
  //      name: 'MPQRCodelist',
  //      meta: {
  //        title: '二维码'
  //      }
  //    },
  //    {
  //      path: 'MPQRRecordlist',
  //      component: () => import('@/views/WeMP/MP_QR_Record_list'),
  //      name: 'MPQRRecordlist',
  //      meta: {
  //        title: '二维码扫描记录'
  //      }
  //    },
  //    {
  //      path: 'MPReplylist',
  //      component: () => import('@/views/WeMP/MP_Reply_Info_list'),
  //      name: 'MPReplylist',
  //      meta: {
  //        title: '自动回复'
  //      }
  //    },
  //    {
  //      path: 'MPReplyKeywordlist',
  //      component: () => import('@/views/WeMP/MP_Reply_Keyword_list'),
  //      name: 'MPReplyKeywordlist',
  //      meta: {
  //        title: '回复内容'
  //      }
  //    },
  //    {
  //      path: 'MPReportInterfacelist',
  //      component: () => import('@/views/WeMP/MP_Report_Interface_list'),
  //      name: 'MPReportInterfacelist',
  //      meta: {
  //        title: '接口报表'
  //      }
  //    },
  //    {
  //      path: 'MPReportMsglist',
  //      component: () => import('@/views/WeMP/MP_Report_Msg_list'),
  //      name: 'MPReportMsglist',
  //      meta: {
  //        title: '消息统计'
  //      }
  //    },
  //    {
  //      path: 'MPReportUserlist',
  //      component: () => import('@/views/WeMP/MP_Report_User_list'),
  //      name: 'MPReportUserlist',
  //      meta: {
  //        title: '用户报表'
  //      }
  //    },
  //    {
  //      path: 'MPSpecialGrouplist',
  //      component: () => import('@/views/WeMP/MP_SpecialGroup_list'),
  //      name: 'MPSpecialGrouplist',
  //      meta: {
  //        title: '高级分组'
  //      }
  //    },
  //    {
  //      path: 'MPUserGrouplist',
  //      component: () => import('@/views/WeMP/MP_UserGroup_list'),
  //      name: 'MPUserGrouplist',
  //      meta: {
  //        title: 'tGroup'
  //      }
  //    },
  //    {
  //      path: 'MPUserGroupRellist',
  //      component: () => import('@/views/WeMP/MP_UserGroup_Rel_list'),
  //      name: 'MPUserGroupRellist',
  //      meta: {
  //        title: 'rel_User_Group'
  //      }
  //    },
  //    {
  //      path: 'MPUserlist',
  //      component: () => import('@/views/WeMP/MP_UserInfo_list'),
  //      name: 'MPUserlist',
  //      meta: {
  //        title: '微信用户'
  //      }
  //    },
  //    {
  //      path: 'MPUserInfoExtlist',
  //      component: () => import('@/views/WeMP/MP_UserInfo_Ext_list'),
  //      name: 'MPUserInfoExtlist',
  //      meta: {
  //        title: 'tWechatUserExt'
  //      }
  //    },
  //    {
  //      path: 'MPUserInfoStatelist',
  //      component: () => import('@/views/WeMP/MP_UserInfo_State_list'),
  //      name: 'MPUserInfoStatelist',
  //      meta: {
  //        title: 'UserState'
  //      }
  //    },
  //    {
  //      path: 'MPUserTaglist',
  //      component: () => import('@/views/WeMP/MP_UserTagInfo_list'),
  //      name: 'MPUserTaglist',
  //      meta: {
  //        title: 'tWechatTag'
  //      }
  //    },
  //    {
  //      path: 'MPUserTagRellist',
  //      component: () => import('@/views/WeMP/MP_UserTag_Rel_list'),
  //      name: 'MPUserTagRellist',
  //      meta: {
  //        title: 'tWechatTag_User'
  //      }
  //    },
  //  ]
  //},
  //{
  //  path: '/WePay',
  //  component: Layout,
  //  redirect: '/',
  //  alwaysShow: true,
  //  name: 'WePay',
  //  meta: {
  //    title: '微信支付',
  //    icon: 'el-icon-menu'
  //  }, children: [
  //    {
  //      path: 'MPReportPaylist',
  //      component: () => import('@/views/WePay/MP_Report_Pay_list'),
  //      name: 'MPReportPaylist',
  //      meta: {
  //        title: '支付报表'
  //      }
  //    },
  //  ]
//},
  {
    id: '800',
    path: '/Activity',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'Activity',
    meta: {
      title: '活动管理',
      icon: require('@/assets/menu/menu1.png')
    },
    children: [
      {
        id: '800001',
        path: 'ActiveInfoList',
        component: () => import('@/views/Activity/Active_Info_list'),
        name: 'ActiveInfoList',
        meta: {
          title: '活动列表'
        }
      },
      {
        id: '800002',
        path: 'AddActivity',
        hidden: true,
        component: () => import('@/views/Activity/Add_Activity'),
        name: 'AddActivity',
        meta: {
          title: '添加活动',
          pre: {
            path: '/Activity/ActiveInfoList',
            meta: { title: '活动列表' }
          }
        }
      }
    ]
  },
  // Survey Management Module
  {
    id: '900',
    path: '/Survey',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'Survey',
    meta: {
      title: '问卷调查',
      icon: require('@/assets/menu/menu1.png')
    },
    children: [
      {
        id: '900001',
        path: 'SurveyList',
        component: () => import('@/views/Survey/Survey_List'),
        name: 'SurveyList',
        meta: {
          title: '问卷列表'
        }
      },
      {
        id: '900002',
        path: 'CreateSurvey',
        hidden: true,
        component: () => import('@/views/Survey/Create_Survey'),
        name: 'CreateSurvey',
        meta: {
          title: '创建问卷',
          pre: {
            path: '/Survey/SurveyList',
            meta: { title: '问卷列表' }
          }
        }
      },
      
    ]
  }
]

export default commonMenuRouter
