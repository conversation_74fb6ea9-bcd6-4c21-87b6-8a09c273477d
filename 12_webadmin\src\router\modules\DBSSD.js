﻿/* Layout */
import Layout from '@/layout'
const commonMenuRouter = [
      {
      path: '/Admin',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'Admin',
      meta: {
        title: '管理端',
        icon: 'el-icon-s-tools'
      }, children: [
          {
          path: 'Userlist',
          component: () => import('@/views/Admin/tUserInfo_list'),
          name: 'Userlist',
          meta: {
              title: '用户信息',
              icon: 'el-icon-user'
              }
          },
      ]
      },
      {
      path: '/Member',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'Member',
      meta: {
        title: '会员用户',
        icon: 'el-icon-user-solid'
      }, children: [
          {
          path: 'Memberlist',
          component: () => import('@/views/Member/t_MemberInfo_list'),
          name: 'Memberlist',
          meta: {
              title: '会员',
              icon: 'el-icon-s-custom'
              }
          },
      ]
      },
      {
      path: '/MenuManage',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'MenuManage',
      meta: {
        title: '菜单配置',
        icon: 'el-icon-menu'
      }, children: [
          {
          path: 'FrontMenulist',
          component: () => import('@/views/MenuManage/t_FrontMenu_list'),
          name: 'FrontMenulist',
          meta: {
              title: '一级菜单',
              icon: 'el-icon-s-grid'
              }
          },
          {
          path: 'FrontMenu2list',
          component: () => import('@/views/MenuManage/t_FrontMenu2_list'),
          name: 'FrontMenu2list',
          meta: {
              title: '二级菜单',
              icon: 'el-icon-s-operation'
              }
          },
      ]
      },
      {
      path: '/News',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'News',
      meta: {
        title: '新闻',
        icon: 'el-icon-document'
      }, children: [
          {
          path: 'Newslist',
          component: () => import('@/views/News/NewsInfo_list'),
          name: 'Newslist',
          meta: {
              title: '新闻信息',
              icon: 'el-icon-news'
              }
          },
      ]
      },
      {
      path: '/PageManage',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'PageManage',
      meta: {
        title: '页面管理',
        icon: 'el-icon-menu'
      }, children: [
          {
          path: 'Componentslist',
          component: () => import('@/views/PageManage/t_Components_list'),
          name: 'Componentslist',
          meta: {
              title: '组件'
              }
          },
          {
          path: 'Pagelist',
          component: () => import('@/views/PageManage/t_Page_list'),
          name: 'Pagelist',
          meta: {
              title: '页面'
              }
          },
          {
          path: 'Templetelist',
          component: () => import('@/views/PageManage/t_Templete_list'),
          name: 'Templetelist',
          meta: {
              title: '模板'
              }
          },
          {
          path: 'Videolist',
          component: () => import('@/views/PageManage/t_Video_list'),
          name: 'Videolist',
          meta: {
              title: '页面资源'
              }
          },
      ]
      },
      {
      path: '/Product',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'Product',
      meta: {
        title: '产品',
        icon: 'el-icon-menu'
      }, children: [
          {
          path: 'ProductCataloglist',
          component: () => import('@/views/Product/tProductCatalog_list'),
          name: 'ProductCataloglist',
          meta: {
              title: '产品大类'
              }
          },
          {
          path: 'ProductCatalog2list',
          component: () => import('@/views/Product/tProductCatalog2_list'),
          name: 'ProductCatalog2list',
          meta: {
              title: '产品小类'
              }
          },
          {
          path: 'ProductDocumentlist',
          component: () => import('@/views/Product/tProductDocument_list'),
          name: 'ProductDocumentlist',
          meta: {
              title: '产品文档'
              }
          },
          {
          path: 'ProductImagelist',
          component: () => import('@/views/Product/tProductImage_list'),
          name: 'ProductImagelist',
          meta: {
              title: '产品图片'
              }
          },
          {
          path: 'Productlist',
          component: () => import('@/views/Product/tProductInfo_list'),
          name: 'Productlist',
          meta: {
              title: '产品信息'
              }
          },
          {
          path: 'ProductVideolist',
          component: () => import('@/views/Product/tProductVideo_list'),
          name: 'ProductVideolist',
          meta: {
              title: '产品视频'
              }
          },
      ]
      },
      {
      path: '/Solution',
      component: Layout,
      redirect: '/',
      alwaysShow: true, 
      name: 'Solution',
      meta: {
        title: '解决方案',
        icon: 'el-icon-menu'
      }, children: [
          {
          path: 'SolutionImagelist',
          component: () => import('@/views/Solution/tSolution_Image_list'),
          name: 'SolutionImagelist',
          meta: {
              title: '方案图片'
              }
          },
          {
          path: 'SolutionDocumentlist',
          component: () => import('@/views/Solution/t_Solution_Document_list'),
          name: 'SolutionDocumentlist',
          meta: {
              title: '方案文档'
              }
          },
          {
          path: 'Solutionlist',
          component: () => import('@/views/Solution/t_Solution_Info_list'),
          name: 'Solutionlist',
          meta: {
              title: '解决方案'
              }
          },
          {
          path: 'SolutionVideolist',
          component: () => import('@/views/Solution/t_Solution_Video_list'),
          name: 'SolutionVideolist',
          meta: {
              title: '方案视频'
              }
          },
      ]
      },

]
export default commonMenuRouter
