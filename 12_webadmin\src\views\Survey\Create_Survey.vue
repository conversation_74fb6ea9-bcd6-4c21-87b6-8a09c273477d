<!--创建/编辑问卷页面-->
<template>
  <div class="create-survey-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑问卷' : '创建问卷' }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-form ref="surveyForm" :model="surveyForm" :rules="rules" label-position="top">
        <el-form-item label="问卷标题" prop="SurveyName">
          <el-input v-model="surveyForm.SurveyName" placeholder="请输入问卷标题" maxlength="100" show-word-limit />
        </el-form-item>
        
        <el-form-item label="问卷描述" prop="SurveyDescription">
          <el-input 
            v-model="surveyForm.SurveyDescription" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入问卷描述" 
            maxlength="500" 
            show-word-limit />
        </el-form-item>
        
        <div class="questions-section">
          <div class="section-header">
            <h3>题目设置</h3>
            <el-button type="primary" size="small" @click="addNewQuestion">添加题目</el-button>
          </div>
          
          <el-collapse v-model="activeNames" v-if="surveyForm.Questions.length > 0">
            <el-collapse-item v-for="(question, index) in surveyForm.Questions" :key="index" :name="index">
              <template slot="title">
                <div class="question-collapse-title">
                  <span class="question-number">{{ index + 1 }}</span>
                  <span class="question-text">{{ question.QuestionText || '未命名题目' }}</span>
                  <span class="question-type">{{ getQuestionTypeLabel(question.QuestionType) }}</span>
                </div>
              </template>
              
              <div class="question-editor">
                <el-form-item :label="`题目 ${index + 1}`" :prop="`Questions.${index}.QuestionText`" 
                  :rules="[{ required: true, message: '请输入题目内容', trigger: 'blur' }]">
                  <el-input v-model="question.QuestionText" placeholder="请输入题目内容" />
                </el-form-item>
                
                <el-form-item label="题目类型">
                  <el-select v-model="question.QuestionType" placeholder="请选择题目类型" @change="(val) => handleQuestionTypeChange(val, index)">
                    <el-option label="单行文本" value="text" />
                    <el-option label="多行文本" value="textarea" />
                    <el-option label="单选题" value="radio" />
                    <el-option label="多选题" value="checkbox" />
                    <el-option label="下拉选择" value="select" />
                    <el-option label="日期选择" value="date" />
                    <el-option label="评分" value="rate" />
                  </el-select>
                </el-form-item>
                
                <el-form-item>
                  <el-checkbox v-model="question.Required">必填</el-checkbox>
                </el-form-item>
                
                <!-- 选项编辑 (单选/多选/下拉) -->
                <div v-if="['radio', 'checkbox', 'select'].includes(question.QuestionType)" class="options-editor">
                  <div class="options-header">
                    <h4>选项设置</h4>
                    <el-button type="text" @click="addOption(index)">添加选项</el-button>
                  </div>
                  
                  <el-form-item 
                    v-for="(option, optIndex) in question.Options" 
                    :key="optIndex"
                    :prop="`Questions.${index}.Options.${optIndex}.OptionText`"
                    :rules="[{ required: true, message: '请输入选项内容', trigger: 'blur' }]"
                    class="option-item">
                    <div class="option-input-group">
                      <el-input v-model="option.OptionText" placeholder="请输入选项内容">
                        <template slot="prepend">
                          <span class="option-number">{{ optIndex + 1 }}</span>
                        </template>
                      </el-input>
                      <el-button 
                        type="danger" 
                        icon="el-icon-delete" 
                        circle 
                        size="mini" 
                        plain
                        @click.prevent="removeOption(index, optIndex)"
                        :disabled="question.Options.length <= 2" />
                    </div>
                  </el-form-item>
                </div>
                
                <!-- 评分设置 -->
                <div v-if="question.QuestionType === 'rate'" class="rate-settings">
                  <el-form-item label="最大分值">
                    <el-input-number v-model="question.RateMax" :min="1" :max="10" />
                  </el-form-item>
                </div>
                
                <div class="question-actions">
                  <el-button type="danger" size="mini" @click="removeQuestion(index)" 
                    :disabled="surveyForm.Questions.length <= 1">删除此题</el-button>
                  <el-button type="primary" size="mini" @click="duplicateQuestion(index)">复制此题</el-button>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
          
          <div v-else class="no-questions">
            <el-empty description="暂无题目，请添加"></el-empty>
          </div>
        </div>
        
        <div class="form-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submitForm('surveyForm')" :loading="submitting">{{ isEdit ? '保存修改' : '创建问卷' }}</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import controller from '@/controllers/Survey/Create_Survey'
export default controller
</script>

<style lang="scss" scoped>
.create-survey-container {
  padding: 20px;
  
  .box-card {
    margin-bottom: 20px;
  }
  
  .questions-section {
    margin: 20px 0;
    border-top: 1px solid #eee;
    padding-top: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }
    
    .no-questions {
      padding: 30px 0;
    }
    
    .question-collapse-title {
      display: flex;
      align-items: center;
      
      .question-number {
        width: 24px;
        height: 24px;
        background-color: #409EFF;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 14px;
      }
      
      .question-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .question-type {
        font-size: 12px;
        color: #909399;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 10px;
        margin-left: 10px;
      }
    }
    
    .question-editor {
      padding: 10px 0;
      
      .options-editor {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
        
        .options-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          
          h4 {
            margin: 0;
            font-size: 14px;
            color: #606266;
          }
        }
        
        .option-item {
          margin-bottom: 10px;
          
          .option-input-group {
            display: flex;
            align-items: center;
            
            .el-input {
              margin-right: 10px;
            }
            
            .option-number {
              width: 20px;
              text-align: center;
              font-weight: bold;
            }
          }
        }
      }
      
      .question-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
        
        .el-button {
          margin-left: 10px;
        }
      }
      
      .rate-settings {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    
    .el-button {
      min-width: 120px;
      margin: 0 10px;
    }
  }
}
</style>
