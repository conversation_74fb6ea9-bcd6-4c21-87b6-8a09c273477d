// not
import * as apis from '@/apis/Survey/Add_Survey'
import { GetGroupSelect, GetTagSelect } from '@/apis/WeChat/Graphic_Push_list'
import { getPublishUrl } from '@/apis/Survey/Survey_Info'
import { fetchSelect as getActiveList } from '@/apis/Activity/Active_Info'
import * as utils from '@/utils'
import { validURL } from '@/utils/validate'
import { tools } from '@/utils/tool'
import { guid } from '@/utils/guid.js'
import draggable from 'vuedraggable'
import collapseTransition from '@/utils/expand.js'
const domainName = process.env.VUE_APP_BASE_API;
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'
var isHaveUploadingError = false;
var isHaveUploading = false;
export default {
  name: 'AddSurvey',
  components: {
    draggable, collapseTransition
  }, // end components

  data() {
    let that = this;
    var validateDate = (rule, value, callback) => {
      console.log(value, 'valuevalue')
      if (!this.questionData.BeginTime) {
        callback(new Error('请选择开始时间'));
      }
      if (!this.questionData.EndTime) {
        callback(new Error('请选择结束时间'));
      }
      callback();
    };
    return {
      domainName: process.env.VUE_APP_BASE_API,
      uselessValue: '',
      defaultToolTitle: '基本个人信息',
      currentToolIndex: 0,
      //userGrouploading: false,
      //userGroupOptions: [],
      wxLabels: [],
      activityOptions: [], // 活动列表选项
      successWayList: [{ name: '显示自定义文案', value: 'text' }, { name: '跳转到指定页面', value: 'specifiedpage' }],
      //{ name: '模板二', value: 'default_2' }, { name: '模板三', value: 'default_3' }
      TemplateNames: [{ name: '模板一', value: 'default_1' },{ name: '模板二', value: 'default_2' }],
      userGroup: [],
      toolData: [],
      questionData: {  //提交的问卷数据
        pcBgImg: '',
        headerImg: '',
        SurveyName: '',
        surveyDes: '',
        BeginTime: '',
        EndTime: '',
        UserGroupIds: [],
        WechatTagIds: '',
        TemplateName: 'default_1',
        IsOnlyMember: false,
        IsAllowDoubleCommit: false,
        //    IsShowVotingResult:'false',
        SurveyStatusId: 0,  //0是草稿,1是正式保存
        ActivityId: '', // 关联活动ID
        successWay: {
          way: 'text',  //text/specifiedpage
          content: '感谢您的作答，请关闭页面',
          pageurl: ''
        },
        currentPage: 1,
        totalPage: 1,
        questions: [],
      },
      loading: false,
      saveLoading: false,
      previewLoading: false,
      rules: {
        SurveyName: [
          { required: true, message: '请输入问卷标题', trigger: 'blur' }
        ],
        BeginTime: [
          { validator: validateDate, required: true, trigger: 'change' }
        ],
        TemplateName: [
          { required: true, message: '请选择模版', trigger: 'change' }
        ]
      },
      relatedQuestions: {
        relatedQuestions: [{ relatedQuestion: '', relatedChoice: [], relatedChoices: [], isFirst: true, choiceIsAnd: '' }],
        questionIsAnd: '1'
      }, //关联数据
      relatedItem: {
      },
      dialogTableVisible: false,
      canBeRelatedQuestions: [],
      //    questionId:0, //每道题的唯一标识
      questionId: guid(),
      choiceId: guid(),//每个选项的唯一标识
      jumpDialogVisible: false,
      canBeJumpedQuestions: {
      },
      jumpedQuestions: {
        jumpWay: "0",
        unconditionalJumpNum: '',
        choice: [{ text: '', jumpQuestionNum: '' }]
      },
      jumpWay1: false,
      jumpWay2: false,
      jumpWays: [
        { text: '按选项跳题' },
        { text: '无条件跳题' }
      ],
      jumpedItem: {},
      showSuccessChoice: false,
      successWay: {
        way: 'text',  //text/specifiedpage
        content: "感谢您的作答，请关闭页面",
        pageurl: ''
      },
      urlprotocol:'https://',
      urllink:"",
      successWayDialogVisible: false,
      previewDialog: false,
      preUrl: '',
      startPickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        },
      },
      endPickerOptions: {
        disabledDate(time) {
          const curDate = Date.now() - 8.64e7 //new Date().getTime();
          const tomorrow = curDate + 24 * 3600 * 1000;
          if (that.questionData.BeginTime) {
            return time.getTime() < new Date(that.questionData.BeginTime).getTime();
          }
          //return time.getTime() < tomorrow;
        },
      },
      dateRange:[],
      activeTool:null,
      editorConfig: {
        // 编辑器不自动被内容撑高
        autoHeightEnabled: false,
        // 初始容器高度
        initialFrameHeight: 253,
        // 初始容器宽度
        initialFrameWidth: 420,
        //initialFrameWidth: '100%',
        zIndex: 9999,
        // 访问 UEditor 静态资源的根路径，可参考 https://hc199421.gitee.io/vue-ueditor-wrap/#/faq
        imageUrlPrefix: '',//单图多图上传图片前缀
        catcherUrlPrefix: '',//秀米转存图片前缀
        UEDITOR_HOME_URL: process.env.VUE_APP_PUBLIC_PATH + '/UEditor/',
        catchRemoteImageEnable: true, // 抓取远程图片
        // 服务端接口（这个地址是我为了方便各位体验文件上传功能搭建的临时接口，请勿在生产环境使用！！！）
        serverUrl: domainName + 'api/UEditor',
        headers: {
          Authorization: 'Bearer ' + getToken(),
        },
        toolbars: [[
          'fullscreen', 'source', '|', 'undo', 'redo', '|',
          'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
          'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
          'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
          'directionalityltr', 'directionalityrtl', 'indent', '|',
          'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
          'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
          'simpleupload', 'insertimage', 'insertframe'
        ]]
      },
      editorDependencies: [
        'ueditor.config.js',
        'ueditor.all.min.js',
        // 添加秀米相关的资源
        //'xiumi/xiumi-ue-dialog-v5.js',
        //'xiumi/xiumi-ue-v5.css',
      ],
    }
  }, // end data
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
  },
  created() {
    this.toolData = JSON.parse(JSON.stringify(tools));
    //this.loadWxGroup();
    //this.loadUserGroup();
    const surveyId = this.$iStorage.get('surveyId')
    if (surveyId) {
      this.getById(surveyId)
    }
    window.catchremoteimageBegin = function () {
      isHaveUploading = true;
      isHaveUploadingError = false;
      console.info('上传开始')
    };

    window.catchremoteimageSuccess = () => {
      isHaveUploading = false;
      isHaveUploadingError = false;
      console.info('上传成功')

      this.$message({
        type: 'success',
        message: '上传图片成功!'
      })
    };

    window.catchremoteimageError = () => {
      isHaveUploading = false;
      isHaveUploadingError = true;
      console.info('上传失败')
      this.$message({
        type: 'error',
        message: '上传图片失败,请重试。若多次失败请联系技术人员'
      })
    };
    this.loadUserGroup();
    this.loadActivityOptions();
    document.addEventListener("keydown", this.handleEnterKey);
    window.addEventListener('message', (event) => {
      if (event.data == 'catchremoteimageBegin') {
        this.catchremoteimageBegin()
      } else if (event.data == 'catchremoteimageSuccess') {
        this.catchremoteimageSuccess()
      } else if (event.data == 'catchremoteimageError') {
        this.catchremoteimageError()
      }
    })
  }, // end create
  mounted() {
  },
  methods: {
    ...utils,
    handleProtocol(){
      if(this.urllink.toLocaleLowerCase().indexOf("http://")!=-1){
        this.urlprotocol='http://'
        this.urllink=this.splitUrl(this.urllink).rest

      }else if(this.urllink.toLocaleLowerCase().indexOf("https://")!=-1){
        this.urlprotocol='https://'
        this.urllink=this.splitUrl(this.urllink).rest
      }
    },
    goback(){
      this.$router.go(-1)
    },
    change(event) {
      console.log(event, 'change...'); //获取旧索引和新索引
      const { newIndex, oldIndex, element } = event.moved;
      const targetItem = this.questionData.questions[newIndex - 1]
      if (newIndex > oldIndex) {
        // 下移
        console.log(targetItem, '下移')
      } else if (newIndex < oldIndex) {
        // 上移
        console.log(targetItem, '上移')
      }
      if(element.type != 'page'){
        if(targetItem.type == 'page'){
          this.questionData.questions[newIndex].pageIndex=targetItem.currentPage
        }else{
          this.questionData.questions[newIndex].pageIndex=targetItem.pageIndex
        }

      }
    },
    onStart() {

    },
    onMove(e) {
      debugger
      const item = e.draggedContext.element
      const index = e.draggedContext.index
      const targetItem = e.relatedContext.element
      const targetIndex = e.relatedContext.index
      //下移
      if(item.type=='page') return false
      if(targetIndex==0) return false
      if (index < targetIndex) {
        if (this.canNotMoveDown(item, index, targetIndex)) return false;
        return true
      }else if(index > targetIndex){ // 上移
        if (this.canNotMoveUp(item, index, targetIndex)) return false;
        if(this.hasMorePage(index)) return false
        return true
      }
      return true
    },
    onEnd() {
    },
    changeTool(index) {
      this.currentToolIndex = index
    },
    openTip(type) {
      this.$alert(type == 1 ? '满足所有关联条件，“当前题目”才出现' : '只要满足一个关联条件，“当前题目”就出现', '提示', {
        confirmButtonText: '知道了',
        callback: action => {
        }
      });
    },
    changeDateRange(date){
      if(date&&date.length){
      this.questionData.BeginTime=date[0]
      this.questionData.EndTime=date[1]
      }else{
        this.questionData.BeginTime=''
        this.questionData.EndTime=''
      }
    },
    changeStartDate(data) {
      if (data) {
        this.$refs.endPicker.focus()
        this.questionData.EndTime = ''
      }
    },
    handleAvatarSuccess(res, file) {
      if (res.state == 1) {
        this.questionData.pcBgImg = window.location.origin + '/' + res.rows[0];
        //this.questionData.pcBgImg = window.location.origin + res.url;
      } else {
        this.$message.error('上传失败');
      }
    },
    handleHeaderAvatarSuccess(res, file) {
      if (res.state == 1) {
        this.questionData.headerImg = window.location.origin + res.url;
        // this.questionData.headerImg = URL.createObjectURL(file.raw);
      } else {
        this.$message.error('上传失败');
      }

    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      // if (!isJPG) {
      // this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      // return isJPG && isLt2M;
      return isLt2M;
    },
    loadUserGroup() {
      GetGroupSelect({
        pageIndex: 1,
        pageSize: 10000,
        sortName: '',
        sortOrder: ''
      }).then(response => {
        this.wxLabels = response['rows']
      })
    },
    loadActivityOptions() {
      getActiveList().then(response => {
        if (response.state === 1) {
          this.activityOptions = response.rows.map(item => {
            return { value: item.ID, label: item.Title }
          })
        }
      })
    },
    showPreview(i, isShow) {
      i.showPreview = isShow;
    },
    addQuestion(item) {
      const tempItem = JSON.parse(JSON.stringify(item))
      //if (tempItem.desc == 'tele') {
      //  tempItem.isCode = false;//是否使用手机验证码
      //  tempItem.isRepeat = true;
      //}
      //收起前面的
      this.questionData.questions.forEach(i => {
        if(i.type!='page'){
          i.isOpen = false;
        }
      });
      // 给选项添加id
      if (tempItem.choice) {
        // tempItem.IsShowVotingResult = false;
        if (tempItem.type == 'radio' || tempItem.type == 'checkbox') {
          tempItem.choice.forEach((a, b) => {
            a.choiceId = this.choiceId;
            a.hasOther = false;
            this.choiceId = guid();
          });
        } else {
          tempItem.choice.forEach((a, b) => {
            a.choiceId = this.choiceId;
            this.choiceId = guid();
          });
        }

      }
      if (tempItem.content) {
        tempItem.content.forEach((a, b) => {
          a.choiceId = this.choiceId;
          this.choiceId = guid();
        });
      }
      Object.assign(tempItem, { isOpen: true, pageIndex: this.questionData.totalPage, questionId: this.questionId });
      this.questionId = guid();
      if (this.questionData.questions.length == 0) {
        this.questionData.questions.push({
          currentPage: this.questionData.totalPage,
          type: 'page'
        });
      }
      this.questionData.questions.push(tempItem);
    },
    hasMorePage(index){
      let flag=false
      if (index == this.questionData.questions.length - 2 && this.questionData.questions[index + 1].type == 'page' && this.questionData.questions[index - 1].type == 'page') {
        this.$message({
          message: '最后只可以添加一个新分页，请添加题目或删除分页后再操作！',
          type: 'warning'
        })
        flag = true
        return flag
      } else if (index == this.questionData.questions.length - 1 && this.questionData.questions[index - 1].type == 'page') {
        if (this.questionData.questions[index - 2] && this.questionData.questions[index - 2].type == 'page') {
          this.$message({
            message: '最后只可以添加一个新分页，请添加题目或删除分页后再操作！',
            type: 'warning'
          });
          flag = true
          return flag
        }
      }
      debugger
      return flag
    },
    //删除题目
    deleteItem(item, index) {
      if (this.canNotDeleted(item, index)) {
        return
      }
      debugger
      if(this.hasMorePage(index)) return
      this.questionData.questions.splice(index, 1);
    },
    //复制题目
    copyItem(item, index) {
      const temp = JSON.parse(JSON.stringify(item))
      this.$delete(temp, 'unconditionalJumpNum')
      this.$delete(temp, 'relatedQuestions')
      this.$delete(temp, 'questionIsAnd')
      this.$set(temp, 'jumpWay', 0)
      this.$set(temp, 'questionId', this.questionId)
      this.questionId = guid();
      if (temp.choice) {
        temp.choice.forEach((a, b) => {
          a.choiceId = this.choiceId;
          this.choiceId = guid();
        })

      }
      this.questionData.questions.splice(index + 1, 0, temp);
    },
    //添加分页
    addPage(item, index) {
      if (index==this.questionData.questions.length-2 && this.questionData.questions[this.questionData.questions.length-1].type=='page') {
        this.$message({
          message: '已添加分页，尚未添加题目！',
          type: 'warning'
        });
        return
      }
      this.questionData.totalPage++
      this.questionData.questions.splice(index + 1, 0, {
        currentPage: item.pageIndex + 1,
        type: 'page'
      });
      debugger
      this.questionData.questions.forEach((i, v) => {
        if (v > index + 1) {
          if (i.type != 'page') {
            i.pageIndex++;
          } else {
            i.currentPage++
          }
        }
      })


    },
    deletePage(index) {
      this.questionData.questions.forEach((i, v) => {
        if (v > index) {
          if (i.type != 'page') {
            i.pageIndex--;
          } else {
            i.currentPage--
          }
        }
      })
      this.questionData.questions.splice(index, 1);
      this.questionData.totalPage--;
    },
    // 上移分页
    moveUpPage(item, index, pageV) {
      this.deletePage(item, index, pageV);
      this.addPage('item', index - 2);
    },
    moveDownPage(item, index, pageV) {
      this.deletePage(item, index, pageV);
      this.addPage('item', index);
    },
    moveFirstPage(item, index, pageV) {
      this.deletePage(item, index, pageV);
      this.addPage('item', -1);
    },
    moveLastPage(item, index, pageV) {
      this.deletePage(item, index, pageV);
      this.addPage('item', this.questionData.questions.length - 1);
    },
    addChoice(item) {
      var choice = {}
      if (item.label.indexOf('评分') >= 0) {
        choice = { text: '', score: 0, choiceId: this.choiceId };
      } else {
        choice = { text: '', choiceId: this.choiceId };
      }
      if (item.type == 'radio' || item.type == 'checkbox') {
        choice = { ...choice, hasOther: false }
      }
      item.choice.push(choice);
      this.choiceId = guid();
    },
    delChoice(item, v) {
      if (item.choice.length <= 2) {
        this.$message({
          message: '选项最少为2个',
          type: 'warning'
        });
        return
      }
      item.choice.splice(v, 1);
    },
    moveupChoice(item, i, v) {
      if (v > 0) {
        item.choice[v] = item.choice.splice(v - 1, 1, i)[0];
      }
    },
    movedownChoice(item, i, v) {
      if (v < item.choice.length - 1) {
        item.choice[v] = item.choice.splice(v + 1, 1, i)[0];
      }
    },
    chooseOther(i) {
      i.hasOther = !i.hasOther;
    },
    // 判断是否被关联
    beRelated(item, index, text) {
      let isRelated = false;
      isRelated = this.questionData.questions.some((i, v) => {
        if (v > index && i.relatedQuestions) {
          return i.relatedQuestions.some(a => {
            return a.relatedQuestion == item.questionId;
          })
        }
      })
      if (isRelated && text) {
        this.$message({
          message: text,
          type: 'warning'
        });
      }
      return isRelated;
    },
    // 判断是否被跳题
    beJumped(item, index, text) {
      let isJumped = false;
      isJumped = this.questionData.questions.some((i, v) => {
        if (v < index) {
          return (i.jumpWay == 1 && i.unconditionalJumpNum == item.questionId) || (i.jumpWay == 2 && i.choice.some(a => {
            return a.jumpQuestionNum == item.questionId;
          }))
        }
      })
      if (isJumped && text) {
        this.$message({
          message: text,
          type: 'warning'
        });
      }
      return isJumped;
    },
    // 判断是否可删除
    canNotDeleted(item, index) {
      return this.beRelated(item, index, '此题已被关联，不可删除') || this.beJumped(item, index, '含有已被跳题，不可删除');
    },

    canNotMoveDown(item, index, targetIndex) {
      let isFalse = false;
      this.questionData.questions.forEach((i, v) => {
          if(v>index && v<=targetIndex){
            if (i.relatedQuestions && i.relatedQuestions.some(a => {
              return a.relatedQuestion == item.questionId;
            })) {
              this.$message({
                message: '不可下移至关联题目之后',
                type: 'warning'
              });
              isFalse = true
              return isFalse;
            }

            if (item.jumpWay == 1) {
              if (item.unconditionalJumpNum == i.questionId) {
                this.$message({
                  message: '不可下移至被跳题之后',
                  type: 'warning'
                });
                isFalse = true
                return isFalse;
              }
            } else if (item.jumpWay == 2) {
              var isJumped = item.choice.some(a => {
                return a.jumpQuestionNum == i.questionId;
              })
              if (isJumped) {
                this.$message({
                  message: '不可下移至被跳题之后',
                  type: 'warning'
                });
                isFalse = true
                return isFalse;
              }
            }
          }
        }
      )

      return isFalse;
    },
    canNotMoveUp(item, index, targetIndex) {
      let isFalse = false;
      let relateds = [];
      this.questionData.questions.forEach((i, v) => {
        if (v < index && v>= targetIndex) {
          if (item.relatedQuestions) {
            let b = item.relatedQuestions.some(a => {
              return a.relatedQuestion == i.questionId;
            })
            if (b) {
              relateds.push(v);
            }
          }
          if (i.jumpWay == 1) {
            if (i.unconditionalJumpNum == item.questionId) {
              this.$message({
                message: '不可移至跳题之前',
                type: 'warning'
              });
              isFalse = true
              return isFalse;
            }
          } else if (i.jumpWay == 2) {
            var isJumped = i.choice.some(a => {
              return a.jumpQuestionNum == item.questionId;
            })
            if (isJumped) {
              this.$message({
                message: '不可移至跳题之前',
                type: 'warning'
              });
              isFalse = true
              return isFalse;
            }
          }
        }
      })
      if (relateds.length) {
        this.$message({
          message: '不可移至被关联题目之前',
          type: 'warning'
        });
        isFalse = true
        return isFalse;
      }
      // this.questionData.questions.forEach((i,v)=>{

      // })
      return isFalse;
    },



    addRelation(item, index) {
      this.questionData.questions.forEach((i, v) => {
        if(i.type != 'page'){
          //v + 1 + '.' +
          i.RelatedName = i.title + '[' + i.catalog + ']';
        }
      })
      this.canBeRelatedQuestions = this.questionData.questions.filter((i, v) => {
        return v < index && i.choice;
      })
      if (item.isRelated) {
        this.relatedQuestions.relatedQuestions = JSON.parse(JSON.stringify(item.relatedQuestions));
        this.relatedQuestions.questionIsAnd = item.questionIsAnd;
      }
      this.dialogTableVisible = true;
      this.relatedItem = item;
    },
    changeRelatedQuestion(item, index) {
      const templatePage = this.relatedQuestions.relatedQuestions.filter((i, v) => {
        return v != index;
      })
      if (templatePage.some((i, v) => { return i.relatedQuestion === item.relatedQuestion })) {
        this.$message({
          message: '关联题目不能重复',
          type: 'warning'
        });
        item.relatedQuestion = '';
        return
      }
      if (item.relatedQuestion === '') return
      let result = this.canBeRelatedQuestions.find(function (i, v) { return i.questionId == item.relatedQuestion });
      item.relatedChoices = result.choice;
      item.catalog = result.catalog;
      item.relatedChoice = [];
      if (result.catalog == '多选') {
        item.choiceIsAnd = '2';
      }
    },
    changeJumpWay(name) {
      this[name] = !this[name]
      if (name == 'jumpWay1') {
        if (this.jumpWay1) {
          this.jumpWay2 = false;
          this.jumpedQuestions.jumpWay = "1";
        }
      }
      if (name == 'jumpWay2') {
        if (this.jumpWay2) {
          this.jumpWay1 = false;
          this.jumpedQuestions.jumpWay = "2";
        }
      }
      if (!this.jumpWay1 && !this.jumpWay2) {
        this.jumpedQuestions.jumpWay = "0";
      }
      // debugger
    },
    moreRelatedQuestion(item, index) {
      if (item.isFirst) {
        if (this.relatedQuestions.relatedQuestions.length >= this.canBeRelatedQuestions.length) {
          this.$message({
            message: '不可超过可关联题目数量',
            type: 'warning'
          });
          return;
        }
        this.relatedQuestions.relatedQuestions.push({ relatedQuestion: '', relatedChoice: [], relatedChoices: [], choiceIsAnd: '' });
      } else {
        this.relatedQuestions.relatedQuestions.splice(index, 1);
      }
    },
    //保存题目关联
    saveRelatedQuestion() {
      this.relatedQuestions.relatedQuestions = this.relatedQuestions.relatedQuestions.filter(i => i.relatedChoice.length > 0);
      if (this.relatedQuestions.relatedQuestions.length > 0) {
        this.relatedItem.isRelated = true;
      }
      Object.assign(this.relatedItem, this.relatedQuestions);
      this.closeRelatedQuestion();
      console.log(this.questionData, 'questionData')
    },
    //删除本题关联
    deleteRelatedQuestion() {
      this.$confirm('确定删除此题的关联逻辑吗？', '删除本题关联', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.$delete(this.relatedItem, 'isRelated')
          this.$delete(this.relatedItem, 'questionIsAnd')
          this.$delete(this.relatedItem, 'relatedQuestions')
          this.closeRelatedQuestion();
        })
        .catch(action => {
        });
    },
    //删除所有题目关联
    deleteAllRelatedQuestions() {
      this.$confirm('确认后将清空本问卷所有题目关联！', '删除所有题目关联', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.questionData.questions.forEach((i, v) => {
            this.$delete(i, 'isRelated')
            this.$delete(i, 'questionIsAnd')
            this.$delete(i, 'relatedQuestions')
            this.closeRelatedQuestion();
          });
        })
        .catch(action => {
        });
    },
    closeRelatedQuestion() {
      this.dialogTableVisible = false;
      this.relatedQuestions = {
        relatedQuestions: [{ relatedQuestion: '', relatedChoice: [], relatedChoices: [], isFirst: true, choiceIsAnd: '' }],
        questionIsAnd: '1'
      };
    },
    addJump(item, index) {
      this.questionData.questions.forEach((i, v) => {
        if(i.type != 'page'){
          //v + 1 + '.' +
          i.RelatedName = i.title;
        }
      })
      this.canBeJumpedQuestions = this.questionData.questions.filter((i, v) => {
        return v > index && i.type != 'page';
      })
      if (item.jumpWay) {
        if (item.jumpWay == 1) {
          this.jumpedQuestions.unconditionalJumpNum = item.unconditionalJumpNum;
          this.jumpWay1 = true;
        } else {
          this.jumpWay2 = true;
        }
        this.jumpedQuestions.jumpWay = item.jumpWay;
      }
      if (item.choice) {
        if (!item.jumpWay || item.jumpWay == 1) {
          item.choice.forEach((a, b) => {
            a.jumpQuestionNum = ""
          });
        }
        this.jumpedQuestions.choice = JSON.parse(JSON.stringify(item.choice))
      }
      this.jumpDialogVisible = true;
      this.jumpedItem = item;
      console.log(this.canBeJumpedQuestions, 'this.canBeJumpedQuestions=')
    },
    saveJumpedQuestion() {

      // choice:[  //可以添加删除，不能小于2
      //     {text:'选项1',score:1,jumpQuestionNum:0,isJump:false},
      //     {text:'选项2',score:3,jumpQuestionNum:5,isJump:true}
      // ],
      // isRequired:false,  // 选了才能提交/翻页
      // jumpWay:0,  //0不跳，1，无条件跳题，不为空时，2根据选项跳,//不为空0，添加chang事件
      // unconditionalJumpNum:1,   //跳到的题不可跳到该题前面,
      const temp = this.jumpedQuestions.choice.every(i => { return !i.jumpQuestionNum })
      if ((this.jumpedQuestions.jumpWay == 1 && !this.jumpedQuestions.unconditionalJumpNum) || (this.jumpedQuestions.jumpWay == 2 && temp)) {
        this.jumpedQuestions.jumpWay = 0
      }
      this.jumpedItem.jumpWay = this.jumpedQuestions.jumpWay;
      if (this.jumpedQuestions.jumpWay == 0) {
      } else if (this.jumpedQuestions.jumpWay == 1) {
        this.jumpedItem.unconditionalJumpNum = this.jumpedQuestions.unconditionalJumpNum;
      } else {
        Object.assign(this.jumpedItem.choice, this.jumpedQuestions.choice);
      }
      console.log(this.questionData, 'questionData')
      this.closeJumpedQuestion();
    },
    closeJumpedQuestion() {
      this.jumpDialogVisible = false;
      this.jumpedQuestions = {
        jumpWay: "0",
        unconditionalJumpNum: '',
        choice: [{ text: '', jumpQuestionNum: '' }]
      };
      this.jumpWay1 = false;
      this.jumpWay2 = false;
    },
    toggleSuccessWayDialog() {
      this.successWayDialogVisible = !this.successWayDialogVisible;
    },
    ready(editorInstance) {
      this.editorInstance = editorInstance;
    },
    saveSuccessWay() {
      if (isHaveUploadingError) {
        this.$message({
          type: 'error',
          message: '拉取图片出错,请重试'
        })
        return
      }
      if (isHaveUploading) {
        this.$message({
          type: 'error',
          message: '正在拉取图片中,不能保存'
        })
        return
      }
      if (this.successWay.way == 'text' && this.editorInstance.queryCommandState("source") != 0) {
        this.$message({
            type: "error",
            message: "当前为源码模式，请转回视图模式再继续操作",
          });
          return
      }
      if(this.successWay.way == 'specifiedpage'){
        this.successWay.pageurl=this.urllink?this.urlprotocol+this.urllink:''
        if(this.successWay.pageurl && !validURL(this.successWay.pageurl)){
          this.$message({
            type: 'error',
            message: '链接地址不正确'
          })
          return
        }

      }
      this.toggleSuccessWayDialog();
      this.questionData.successWay = this.successWay;
    },
    copyurl(SurveyId) {

    },
    goPreview() {
      this.subSurvey('goPreview', (result) => {
        debugger
        getPublishUrl({ SurveyId: result.rows[0].SurveyId }).then(response => {
          if (response.state == 1 && response.data) {
            this.previewLoading = false;
            this.previewDialog = true
            this.preUrl = response.data.content + '&openid=test&isPreview=true&v=' + new Date().getTime()
            //"https://localhost:7265/SurveyInfo/?openid=test&Surveyid=" + this.questionData.surveyId+"&isPreview=true&v="+new Date().getTime()
          } else {
            this.$message({
              message: response.msg,
              type: 'error'
            });
          }
        })
        //this.$router.push({
        //  name:'PreviewSurvey',
        //  query:{Surveyid:result.rows[0].SurveyId}
        //})
      })
    },
    test(){

    },
    subSurvey(type, callback) {
      this.$refs['form'].validate((valid) => {
        if (valid || type == 'goPreview') {
          const hasMorePage = this.questionData.questions.some((i, v) => {
            return (i.type == 'page' && this.questionData.questions[v + 1] && this.questionData.questions[v + 1].type == 'page') || (v==this.questionData.questions.length-1 && i.type=='page')
          })
          if (hasMorePage) {
            this.$message({
              message: '请删除多余的分页！',
              type: 'warning'
            });
            return
          }
          if (!this.questionData.questions.length) {
            this.$message({
              message: '请至少添加一道题目！',
              type: 'warning'
            });
            return
          }
          if (type == 'goPreview' || this.checkData()) {
            if (type == 'goPreview') {
              this.previewLoading = true;
              this.questionData.SurveyStatusId = 0;
            } else {
              this.saveLoading = true;
              this.questionData.SurveyStatusId = 1;
            }
            var questionData=JSON.parse(JSON.stringify(this.questionData))
            questionData.questions = questionData.questions.filter(i=>{
              return i.type != 'page'
            })
            console.log(this.questionData, 'this.questionData')
            if (!this.questionData.surveyId) {
              apis.createModel({ JsonData: JSON.stringify(questionData) }).then(res => {
                this.saveLoading = false;
                if (res.state == 1) {
                  if (type == 'goPreview') {
                    this.questionData.surveyId =res.rows[0].SurveyId
                    callback(res)
                  } else {
                    this.$router.replace({
                      name: 'Surveylist',
                    })
                    //window.location.href = "/Survey/"
                  }
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error'
                  });
                }
              }).catch(e=>{
                this.saveLoading = false;
              })
            } else {
              apis.updateModel({ JsonData: JSON.stringify(questionData) }).then(res => {
                this.saveLoading = false;
                if (res.state == 1) {
                  if (type == 'goPreview') {
                    this.questionData.surveyId =res.rows[0].SurveyId
                    callback(res)
                  } else {
                    this.$router.replace({
                      name: 'Surveylist',
                    })
                  }
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error'
                  });
                }
              }).catch(e=>{
                this.saveLoading = false;
              })
            }
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });

    },
    checkData() {
      let flag = true;
      if (this.questionData.SurveyName == '') {
        this.$message({
          message: '问卷名不能为空',
          type: 'warning'
        });
        this.$refs.surveyNameInput.focus()
        return  flag=false;
      }
      if (this.questionData.BeginTime == '') {
        this.$message({
          message: '开始时间不能为空',
          type: 'warning'
        });
        this.$refs.beginPicker.focus()
        return flag=false;
      }
      if (this.questionData.EndTime == '') {
        this.$message({
          message: '结束时间不能为空',
          type: 'warning'
        });
        this.$refs.endPicker.focus()
        return flag=false;
      }

      this.questionData.questions.forEach((i, v) => {
        if(i.type != 'page'){
          if (i.title == '') {
            this.$message({
              message: '问题' + (v + 1) + '标题不能为空',
              type: 'warning'
            });
            return flag=false;
          }
          if (i.choice && i.choice.some(i => { return i.text == '' })) {
            this.$message({
              message: '问题' + (v + 1) + '选项不能为空',
              type: 'warning'
            });
            return flag=false;
          }
        }
      })
      return flag;
    },
    getById(id) {
      this.loading = true;
      apis.getById(id).then(response => {
        this.loading = false;
        if (response.state == 1) {
debugger
          let questionData = JSON.parse(response.rows[0].JsonData);
          let pageIndex=0,questions=[]
          questionData.questions.forEach(i=>{
            if(i.pageIndex!=pageIndex){
              pageIndex=i.pageIndex
              questions.push({
                currentPage: pageIndex,
          type: 'page'
              })
            }
            questions.push(i)
          })
          questionData.questions=questions
          this.questionData =questionData
          this.dateRange=[this.questionData.BeginTime,this.questionData.EndTime]
          this.questionData.surveyId = id;
          this.successWay = this.questionData.successWay
          // 确保ActivityId字段存在
          if(!this.questionData.hasOwnProperty('ActivityId')) {
            this.questionData.ActivityId = ''
          }
          console.log(this.successWay,'this.successWay')
          if(this.successWay.pageurl){
            this.urlprotocol=this.splitUrl(this.successWay.pageurl).protocol
            this.urllink=this.splitUrl(this.successWay.pageurl).rest
          }
        } else {
          this.loading = false;
          this.$message({
            message: response.msg,
            type: 'error'
          });
        }
      })
    },
  }
}
