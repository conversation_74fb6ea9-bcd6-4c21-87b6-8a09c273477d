<!--not若不修改去掉空格-->
<template>
  <div class="flex-between grid-content survey" v-loading="loading">
    <div class="left">
      <div class="top-header">
        {{ questionData.surveyId ? "编辑问卷调查" : "添加问卷调查" }}
      </div>
      <el-form
        :model="questionData"
        :rules="rules"
        ref="form"
        label-position="top"
        class="form"
      >
       
        <el-form-item label="问卷标题" prop="SurveyName">
       
          <el-input
            ref="surveyNameInput"
            type="textarea"
            :rows="3" style="height:70px;"
            placeholder="问卷标题"
            v-model="questionData.SurveyName"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="起止时间" prop="BeginTime">
          <!--flex-center-between -->
          <div class="date-box">
    <el-date-picker
    v-model="dateRange"
    style="width: 100%"
      type="daterange"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
      start-placeholder=""
      end-placeholder="" @change="changeDateRange">
    </el-date-picker>
           
          </div>
        </el-form-item>
        <el-form-item label="问卷描述" prop="surveyDes">
          <el-input
            type="textarea"
            :rows="3" style="height:70px;"
            placeholder="问卷描述"
            v-model="questionData.surveyDes"
          >
          </el-input>
        </el-form-item>
      
        <el-form-item label="选择模版" prop="TemplateName">
          <el-select
            v-model="questionData.TemplateName"
            filterable
            clearable
            placeholder=""
          >
            <el-option
              v-for="item in TemplateNames"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关联活动" prop="ActivityId">
          <el-select
            v-model="questionData.ActivityId"
            filterable
            clearable
            placeholder="请选择关联活动"
          >
            <el-option
              v-for="item in activityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="Title">
          <div class="switch-box">
          允许重复提交
          <el-switch
            v-model="questionData.IsAllowDoubleCommit"
            :width="45"
          >
          </el-switch>
          </div>
        </el-form-item>
        <el-form-item label="" prop="Title">
          <div class="way-box">
          <div class="title">
            提交问卷后处理方式<span v-on:click="toggleSuccessWayDialog">去设置</span>
          </div>
  <el-radio-group v-model="successWay.way">
    <el-radio v-for="i in successWayList" :key="i.value" :label="i.value">{{ i.name }}</el-radio>
  </el-radio-group>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-between right">
      <!-- 左边题目区域 -->
      <div class="bottom-left">
        <div class="bottom-left-title">添加问题</div>
        <div v-if="!questionData.questions.length" class="nodata">
          点击右侧添加问题
        </div>
        <template v-else>
          <draggable v-model="questionData.questions" ghost-class="ghost"
    chosen-class="chosenClass" forceFallback="true" group="people" animation="1000"
          delay="200" @start="onStart" @end="onEnd" @change="change" :move="onMove" filter=".unDrag">
    <transition-group>
          <div
            class="quenstion-list"
            v-for="(item, index) in questionData.questions"
            :key="index"
          >
            <div
              class="unDrag flex-center-between question-list-page-tit"
              v-if="item.type=='page'"
            >
              <div class="question-list-tit-title">
                [第{{ item.currentPage }}页/共{{ questionData.totalPage }}页]
              </div>
              <img v-if="index"
                    v-on:click.stop.prevent="deletePage(index)" src="@/assets/images/delete2.png" alt="" />
            </div>
            <div v-else class="flex-center-between question-list-tit">
              <!--问题{{ index + 1 }}-->
              <span class="question-list-tit-title">{{item.title}}</span>
              <div class="question-list-tit-right">
                <div class="question-type">{{item.label}}</div>
             
                    <div class="flex-center operationBox operationBox2">
                      <img
                    class="delete"
                      src="@/assets/images/delete.png"
                      alt="" v-on:click.stop.prevent="deleteItem(item, index)"
                    />
                    <img
                      src="@/assets/images/copy.png"
                      alt="" v-on:click.stop.prevent="copyItem(item, index)"
                    />
                    <img class="arrow" :class="{ unexpand: !item.isOpen }"
                    v-on:click.stop.prevent="item.isOpen = !item.isOpen" src="@/assets/images/expand.png" alt="" />
                    </div>
              </div>
            </div>
            <collapse-transition>
              <div class="divanswer" v-show="item.isOpen">
                <div class="divanswer-ques">
                  <span class="spanTitle">标题</span>
       
          <el-input
            ref="QuestionName"
            type="textarea"
            style="height:70px;"
            v-model="item.title"
            placeholder=""
            @click.stop.prevent
          ></el-input>
                </div>
                <div class="divanswer-choice" v-if="item.choice && item.choice.length">
                  <div v-for="(i, v) in item.choice"
                    :key="v">
<span class="spanTitle">选项{{ v + 1 }}</span>
<div class="flex divanswer-choice-box">
<el-input
            type="text"
            v-model.trim="i.text"
            placeholder=""
            @click.stop.prevent
          ></el-input>
                    <template v-if="i.score || i.score == 0">
                      <el-input-number v-model="i.score"
            @click.stop.prevent controls-position="right" ></el-input-number>分
                    </template>
                    
                    <img class="delAnswer" src="@/assets/images/delete.png" v-on:click.stop.prevent="delChoice(item, v)"/>
                    <img v-if="v != 0" src="@/assets/images/move-up2.png" v-on:click.stop.prevent="moveupChoice(item, i, v)" />
                    <img v-if="v != item.choice.length - 1" src="@/assets/images/move-down2.png" v-on:click.stop.prevent="movedownChoice(item, i, v)" />
                    <template v-if="item.type == 'radio' || item.type == 'checkbox'">
                      <img
                        v-if="i.hasOther"
                        src="@/assets/images/login_check.png" v-on:click.stop.prevent="chooseOther(i)"
                      />
                      <img
                        v-else
                        src="@/assets/images/no-other.png"
                        v-on:click.stop.prevent="chooseOther(i)"
                      />
                    </template>
                    <img v-if="v == item.choice.length - 1" src="@/assets/images/copy2.png" v-on:click.stop.prevent="addChoice(item)"/>
</div>
                  </div>
                </div>
                <div class="divanswer-choice" v-if="item.content && item.content.length">
                  <div
                  v-for="(i, v) in item.content"
                    :key="v">
<span class="spanTitle">标题{{ v + 1 }}</span>
<el-input
            type="text"
            v-model.trim="i.text"
            placeholder=""
            @click.stop.prevent
          ></el-input>
                  </div>
                </div>
                <div class="sliderBox" v-if="item.type == 'slider'">
<div class="flex-center-between ">
                      <div>
                        <span class="spanTitle">最小值：</span>
                        <el-input-number :min="0" v-model="item.minValue" controls-position="right"
            @click.stop.prevent ></el-input-number>
                      </div>
                      <div>
                        <span class="spanTitle sliderText"
                          >最小值显示文本：</span
                        >
                        <el-input
            type="text"
            v-model.trim="item.minText"
            placeholder=""
            @click.stop.prevent
          ></el-input>
                      </div>
                    </div>
                    <div class="flex-center-between ">
                      <div>
                        <span class="spanTitle">最大值：</span>
<el-input-number v-model="item.maxValue" :min="item.minValue?item.minValue:0" controls-position="right"
            @click.stop.prevent ></el-input-number>
                      </div>
                      <div>
                        <span class="spanTitle sliderText"
                          >最大值显示文本：</span
                        >
                        <el-input
            type="text"
            v-model.trim="item.maxText"
            placeholder=""
            @click.stop.prevent
          ></el-input>
                      </div>
                    </div>
                </div>
                  <div class="flex-between divanswer-btn">
                    <div class="bottom-btn-left">
                      <div class="switch-box">
                      必答项
                      <el-switch
                        v-model="item.isRequired"
                        :width="45"
                      >
                      </el-switch>
                      </div>
                      <div v-if="item.choice && item.choice.length" class="switch-box">
                      显示投票结果
                      <el-switch
                        v-model="item.IsShowVotingResult"
                        :width="45"
                      >
                      </el-switch>
                      </div>
                      <template v-if="item.desc == 'tele'">
                      <div class="switch-box">
                      是否可以重复
                      <el-switch
                        v-model="item.isRepeat"
                        :width="45"
                      >
                      </el-switch>
                      </div>
                      <div class="switch-box">
                      是否使用短信验证码
                      <el-switch
                        v-model="item.isCode"
                        :width="45"
                      >
                      </el-switch>
                      </div>
                      </template>
                    </div>
                    <div class="bottom-btn-right">
                      <el-button class="page" type="default" v-on:click.stop.prevent="addPage(item, index)">添加分页</el-button>
                      <el-button class="relate" type="default" v-if="index != 1"
                        v-on:click.stop.prevent="addRelation(item, index)">{{ item.isRelated ? "编辑关联" : "题目关联" }}</el-button>
      <el-button
        type="primary"
        v-if="!(index == questionData.questions.length - 1 || (index == questionData.questions.length - 2 && questionData.questions[questionData.questions.length - 1].type=='page'))"
                        v-on:click.stop.prevent="addJump(item, index)"
        >{{
                          item.jumpWay == 1 || item.jumpWay == 2
                            ? "编辑跳题"
                            : "跳题逻辑"
                        }}</el-button
      >
                    </div>
                  </div>
              </div>
            </collapse-transition>
          </div>
    </transition-group>
</draggable>
        </template>
      </div>
      <!-- 右边题目工具区域 -->
      <div class="bottom-right">
        <div v-for="(item, index) in toolData"
            :key="index">
          <div class="flex-center-between operate" :class="{'activeTool':activeTool==index}" @click="activeTool==index?activeTool=null:activeTool=index">
          <span>{{item.title}}</span>
          <img src="../../assets/images/down.png" alt="" srcset="">
        </div>
        <collapse-transition>
              <div class="link-table" v-show="activeTool==index">
              <ul>
                <li v-for="(i, v) in item.details" :key="v" v-on:click="addQuestion(i)">
                  <div
                  >
                    {{ i.label }}
                  </div>
                </li>
              </ul>
            </div>
            </collapse-transition>
        </div>

     
      </div>
    </div>
     <!--v-show="questionData.questions.length"-->
    <div class="btns">
      <el-button type="default" @click="goback">取消</el-button>
      <el-button type="default" @click="goPreview" :loading="previewLoading">预览</el-button>
      <el-button
        type="primary"
        @click="subSurvey"
        :loading="saveLoading"
        >提交</el-button
      >
    </div>
    <!-- 跳题 -->
    <el-dialog
      title="跳题逻辑"
      :visible.sync="jumpDialogVisible"
      width="562px"
      :before-close="closeJumpedQuestion"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      class="edit-dialog jumpDialog"
    >
        <el-radio-group v-model="jumpedQuestions.jumpWay">
    <el-radio v-if="jumpedItem.catalog == '单选'" label="2">按选项跳题</el-radio>
    <el-radio label="1">无条件跳题</el-radio>
  </el-radio-group>
        <template v-if="jumpedQuestions.jumpWay == 2">
          <div class="conditionalJumpTable">
<div class="flex-center-between title">
                <div class="blank"></div>
                <div class="choice">选项</div>
                <div class="question">跳转到</div>
              </div>
            <div class="flex-center-between content" v-for="(item, index) in jumpedQuestions.choice" :key="index">
                <div class="blank">{{index==0?'选择':''}}</div>
                <div class="choice">{{ item.text }}</div>
                <div class="question">
                  <el-select
                    v-model="item.jumpQuestionNum"
                    class="choiceIsAnd"
                    v-on:change="test(item)"
                    placeholder="不跳转，按顺序填写下一题"
                  >
                    <el-option
                      :key="i.questionId"
                      :label="i.RelatedName"
                      :value="i.questionId"
                      v-for="(i, v) in canBeJumpedQuestions"
                    ></el-option>
                  </el-select>
                </div>
            </div>
          </div>

        </template>
        <div v-if="jumpedQuestions.jumpWay == 1" class="unconditionalJump">
          <div class="unconditionalTip">无条件跳题 ，填写此题后跳转到</div>
          <el-select
            v-model="jumpedQuestions.unconditionalJumpNum"
            class="jumpedQuestions"
            placeholder="不跳转，按顺序填写下一题"
          >
            <el-option
              :key="i.questionId"
              :label="i.RelatedName"
              :value="i.questionId"
              v-for="(i, v) in canBeJumpedQuestions"
            ></el-option>
          </el-select>
        </div>
      <span slot="footer" class="dialog-footer">
        <el-button plain type="info" @click="closeJumpedQuestion">取消</el-button>
        <el-button type="primary" @click="saveJumpedQuestion">确认</el-button>
      </span>
    </el-dialog>
    <!-- 关联题目 -->
    <el-dialog
      title="题目关联"
      :visible.sync="dialogTableVisible"
      width="562px"
      :before-close="closeRelatedQuestion"
      class="edit-dialog relatedDialog"
      :append-to-body="true"
      :close-on-click-modal="false"
    >
      <div class="cont_dis_tip_con">
        <div class="title">
          {{'当前题目:问题' + relatedItem.RelatedName ||
        relatedItem.RelatedName.substring(0, 1)}}
        </div>
        <div
          v-for="(item, index) in relatedQuestions.relatedQuestions"
          class="relatedQuestions"
        >
          <div class="box1 flex-center-between">
            <div class="relationspan">关联题目{{ index + 1 }}</div>
          <el-select
            v-model="item.relatedQuestion"
            placeholder="请选择关联的题目"
            v-on:change="changeRelatedQuestion(item, index)"
          >
            <el-option
              v-for="(i, v) in canBeRelatedQuestions"
              :key="i.questionId"
              :label="i.RelatedName"
              :value="i.questionId"
            >
            </el-option>
          </el-select>
          <img class=" relationMore" v-if="item.isFirst" src="@/assets/images/copy2.png" v-on:click="moreRelatedQuestion(item, index)" alt="" />
            <img v-else class="relationMore" src="@/assets/images/delete.png" v-on:click="moreRelatedQuestion(item, index)"alt="" />
          </div>
          <div
            v-if="
              item.relatedChoices.length &&
              (item.relatedQuestion || item.relatedQuestion === 0)
            " class="box2"
          >
            <div class="relationspan">
              当关联“题目{{ index + 1 }}”选择了以下选项：
            </div>
            <el-checkbox-group v-model="item.relatedChoice">
    <el-checkbox
    v-for="i in item.relatedChoices"
              :key="i.choiceId" :label="i.choiceId">{{i.text}}</el-checkbox>
  </el-checkbox-group>
            <div class="relationspan">
              中的
              <template v-if="!(item.catalog == '多选')"> 任意一个 </template>
              <template v-else>
                <el-select v-model="item.choiceIsAnd" class="choiceIsAnd">
                  <el-option key="1" label="其中一个选项时" value="1"></el-option>
                  <el-option key="2" label="全部选项时" value="2"></el-option>
                </el-select>
              </template>
              ，“当前题目”才出现
            </div>
          </div>
        </div>
        <div class="box3" v-if="relatedQuestions.relatedQuestions.length > 1">
          <div class="relationspan">关联多题时：多题之间</div>
                <el-radio-group v-model="relatedQuestions.questionIsAnd">
    <el-radio :label="1">为“且”的关系<span @click.stop="openTip(1)">( ? )</span></el-radio>
    <el-radio :label="2">为“或”的关系<span @click.stop="openTip(2)">( ? )</span></el-radio>
  </el-radio-group>
        </div>
      </div>
      <span slot="footer" class="flex-center-between dialog-footer">
        <div>
          <el-button type="danger" @click="deleteAllRelatedQuestions"
            >删除所有题目关联</el-button
          >
          <el-button
            type="danger"
            v-if="relatedItem.isRelated"
            class="btn-selfrelate"
            @click="deleteRelatedQuestion"
            >删除本题关联</el-button
          >
        </div>
        <el-button type="primary" @click="saveRelatedQuestion">确认</el-button>
      </span>
    </el-dialog>
    <!--提交后处理方式设置-->
    <el-dialog
      :title="successWay.way == 'specifiedpage'?'跳转指定页面':'自定义文案'"
      :visible.sync="successWayDialogVisible"
      width="562px"
      :before-close="toggleSuccessWayDialog"
      class="edit-dialog successWayDialog"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div class="cont_dis_tip_con">
        <div v-show="successWay.way == 'specifiedpage'">
          <div class="relationspan">输入链接地址</div>
  <el-input placeholder="" v-model.trim="urllink"
            @click.stop.prevent class="input-with-select"
            @change="handleProtocol">
    <el-select v-model="urlprotocol" slot="prepend" placeholder="请选择">
      <el-option label="https://" value="https://"></el-option>
      <el-option label="http://" value="http://"></el-option>
    </el-select>
  </el-input>
       
        </div>
        <div v-show="successWay.way == 'text'">
          <div>
            <vue-ueditor-wrap
              v-model="successWay.content"
              :config="editorConfig"
              editor-id="editor-demo-01"
              :editor-dependencies="editorDependencies"
                    @ready="ready"
            ></vue-ueditor-wrap>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button plain type="info" @click="toggleSuccessWayDialog">取消</el-button>
        <el-button type="primary" @click="saveSuccessWay">确认</el-button>
      </span>
    </el-dialog>
    <!--预览-->
    <el-dialog
      title="问卷预览"
      :visible.sync="previewDialog"
      width="375px"
      class="edit-dialog preview-dialog3"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
<iframe
              :src="preUrl"
              class="iframe"
              height="100%"
              width="100%"
              scrolling="auto"
              frameborder="0"
            ></iframe>
    </el-dialog>
  </div>
</template>
<script>
import controller from "@/controllers/Survey/Add_Survey";
export default controller;
</script>
<style lang="scss">
.survey {
      .el-input__inner {
        height: 44px;
        line-height: 44px;
        background: #f9f9f9;
        border-radius: 8px;
        border: none;
        font-size: 14px;
font-weight: 600;
color: #333333;
padding-left: 17px;
      }
      .el-textarea__inner {
    height: 118px;
    max-height: 100%;
    background: #f9f9f9;
    border-radius: 8px;
    border: none;
        font-size: 14px;
font-weight: 600;
color: #333333;
padding-left: 17px;
}
      .el-radio__label{
        font-size: 13px;
font-weight: 500;
color: #333333!important;
line-height: 18px;
      }
      .el-switch{
        margin-left: 24px;
        .el-switch__core{
        height: 30px;
        border-radius: 15px;
        &::after{
          width: 22px;
          height: 22px;
          left: 3px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      &.is-checked .el-switch__core::after{
        left: 100%;
        margin-left: -25px;
      }
      }

}
.el-dropdown-menu.survey-dropdown{
  width: 274px;
  margin-top: 0!important;
  background: #FFFFFF;
box-shadow: 0px 0px 14px 0px rgba(0,0,0,0.1);
border-radius: 6px;
border: none;
min-width: 116px;
padding: 17px 10px;
.popper__arrow{
  display: none;
}
.el-dropdown-menu__item{
height: 34px;
border-radius: 6px;
  font-size: 14px;
font-weight: 500;
color: #333333;
line-height: 34px;
margin: auto ;
padding: 0 10px;
}
.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
color: #25A4F8;
background: #F1FAFF;

}
}
.edui-editor {
  //min-width: 700px;
}
.edui-default .edui-editor {
  width: 100%!important;
  //background-color: #edeceb !important;
}
.edui-editor-iframeholder {
  width: 100%!important;
  //margin: auto;
  //background-color: white;
}
.jumpDialog,
.relatedDialog{
  .el-dialog__body{
    .el-input__inner {
    height: 38px;
  }
  }
}
.jumpDialog,
.relatedDialog,
.successWayDialog {
  .el-dialog{
min-height: 360px;
  }
  .el-dialog__header{
    padding: 22px 32px;
  }
  .el-dialog__body{
    padding: 59px 32px 50px;
  }
.el-dialog__footer{
  text-align: center;
  button{
    width: 90px;
height: 44px;
    margin:0 12px;
border-radius: 8px;
font-weight: 600;
border: none;
  }
}
}
.jumpDialog,.relatedDialog{
.el-dialog{
min-height: 663px;
padding-bottom: 100px;
  }
  .el-dialog__body{
    padding: 24px 34px;
    .el-input__inner {
              color: #666666;
            }
  }
.el-dialog__footer{
  padding-bottom: 0;
  position: absolute;
  bottom:40px;
  left: 50%;
  transform: translateX(-50%);
}
}
.relatedDialog {
  .el-checkbox__inner{
  width: 20px;
  height: 20px;
  &::after{
  width: 5px;
    height: 10px;
    left: 7px;
}
}
  .el-checkbox__label{
        font-size: 13px;
padding-left: 8px;
font-weight: 500;
color: #333333;
line-height: 18px;
vertical-align: middle;


      }
      .title{
    font-size: 13px;
font-weight: 500;
color: #333333;
line-height: 18px;
margin-bottom: 16px;
  }
    .relationspan {
      font-size: 13px;
font-weight: 500;
color: #333333;
line-height: 18px;
white-space: nowrap;
    }
.relatedQuestions {
    margin-bottom: 24px;
    .box1{
      margin-bottom: 22px;
      .el-select {
      width: 389px;

    }
    .relationspan {
margin-right: 16px;
    }
    .relationMore {
      margin-left: 17px;
      width: 21px;
    }
    }
.box2{
  font-size: 13px;
font-weight: 500;
color: #333333;
line-height: 18px;
.el-checkbox-group{
  margin: 16px 0;
  .el-checkbox{
      display: block;
      &+.el-checkbox{
        margin-top: 16px;
      }

    }
}
.choiceIsAnd {
        width: 131px;
      }
}

  }
  .box3{
.relationspan {
margin-bottom: 16px;
    }
  }
  .el-dialog__footer{
width: 100%;
padding: 0 34px;
.dialog-footer{
  &>div{
    button{
      width: 136px;
      height: 36px;
      padding: 0;
    }
  }
  button{
    margin: 0;
    &.btn-selfrelate {
      margin-left: 16px;
    }
  }
}

  }
}
.preview-dialog3{
  .el-dialog__header{
    padding: 22px 32px;
  }
  .el-dialog__body{
    padding: 0;
    height: 730px;
  }
}
</style>
<style scoped lang="scss">
.ghost {
}
.chosenClass {
}
.grid-content {
  height: calc(100vh - 126px);
  margin: 0 30px;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 80px;
  .btns {
    position: absolute;
    bottom: 20px;
    right: 0;
    z-index: 999;
    color: #999999;
    button {
      width: 80px;
      height: 40px;
      border-radius: 6px;
      font-weight: 500;
      border: none;
      &:nth-child(1),&:nth-child(2) {
        z-index: 999;
        background: #eeeeee;
        margin-right: 16px;
      }
    }
  }
  .left {
    width: 28%;
      max-width: 416px;
    background: #ffffff;
    border-radius: 6px;
    margin-right: 16px;
    padding: 4px 32px;
        overflow: auto;
    .top-header {
      height: 76px;
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 76px;
    }
    .date-box {
      & > span {
        margin: 0 12px;
        font-weight: 500;
        color: #333333;
      }
    }
    .switch-box{
      font-weight: 600;
color: #333333;
line-height: 30px;
    }
    .way-box{
      margin-top: 16px;
      .title{
        font-weight: 600;
color: #333333;
line-height: 20px;
span{
  font-size: 13px;
font-weight: 500;
color: #019EF7;
margin-left: 16px;
text-decoration: underline;
cursor: pointer;
}
      }
    }
  }
  .right {
    flex: 1;
    background: #FFFFFF;
    border-radius: 6px;
    .bottom-left {
      //width: 72%;
      flex: 1;
      position: relative;
      padding: 4px 30px 24px;
          overflow: auto;
      .bottom-left-title {
        height: 76px;
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 76px;
      }
      .nodata {
        position: absolute;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 16px;
font-weight: 500;
color: #DDDDDD;
line-height: 22px;
      }
      .quenstion-list {
        .question-list-page-tit{
          padding: 0 19px 0 24px;
height: 34px;
background: #019EF7;
border-radius: 8px;
line-height: 34px;
font-size: 14px;
font-weight: 500;
color: #FFFFFF;
img{
  width: 21px;
}
        }
        .question-list-tit {
          height: 44px;
background: #DFF4FF;
border-radius: 1px 8px 8px 1px;
          font-size: 14px;
font-weight: 500;
color: #019EF7;
          padding: 0 20px 0 24px;
          position: relative;
          white-space: nowrap;
          &::before{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
height: 44px;
background: #019EF7;
border-radius: 1px;
          }
          .question-list-tit-title {
            overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
            //font-size: 14px;
            //width: 20%;
            //white-space: nowrap;
          }
          .question-list-tit-right {
            white-space: nowrap;
            .question-type{
              display: inline-block;
              vertical-align: middle;
              height: 21px;
background: #FFFFFF;
border-radius: 11px;
font-size: 12px;
padding: 0 10px;
font-weight: 500;
color: #019EF7;
line-height: 21px;
margin-right: 24px;
            }
            .operationBox{
              vertical-align: middle;
              display: inline-flex;
              img{
                margin-right: 8px;
              cursor: pointer;
              &:last-child{
                margin-right: 0;
              }
              }
              &.operationBox1{
                width: 160px;
                text-align: left;
               justify-content: flex-start;
                //margin-right: 48px;
                img{
                  width: 20px;
                  &:nth-child(3){
                  margin-left: 9px;
                }
                }
              }
              &.operationBox2{
                img{
                  width: 21px;

                &.arrow{
                  margin:0 0 0 18px;
                    width: 10px;
    padding: 5px;
    box-sizing: content-box;
        transform: rotate(0deg);
                transition: 0.5s;
    &.unexpand{
            transform: rotate(180deg);
                  transition: 0.5s;
    }
                }
                }
              }
            }
          }
        }
        .divanswer {
          padding: 16px 24px;
              .spanTitle {
                display: block;
                font-size: 14px;
font-weight: 600;
color: #333333;
line-height: 20px;
                margin-bottom: 10px;
              }
          .divanswer-ques{
              margin-bottom: 16px;
          }
          .divanswer-choice{
            .spanTitle{
margin-top: 24px;
            }


            .divanswer-choice-box{
align-items: center;
.el-input{
              width: 50%;
              flex-shrink: 0;
              margin-right:12px;
            }
            .el-input-number{
              width: 100px;
            }
            img{
              width: 21px;
              margin-right: 4px;
              cursor: pointer;
            }
            }
          }
          .sliderBox{
            &>div{
              &>div{
                width: 40%;
                &:nth-child(2){
                  width: 58%;
                }
                .el-input-number{
                  width: 100%;
                }
              }
            }
          }
          .divanswer-btn {
            margin-top: 16px;
            align-items: flex-end;
            .bottom-btn-left{
              flex: 1;
            .switch-box{
              display: inline-block;
              font-size: 14px;
font-weight: 600;
color: #333333;
line-height: 30px;
margin-right: 40px;
white-space: nowrap;
.el-switch{
  margin-left: 16px;
}
            }
            }
                .bottom-btn-right {
    text-align: right;
    button {
      width: 90px;
      height: 34px;
border-radius: 8px;
      font-weight:600;
      font-size: 13px;
      border: none;
        margin-left: 16px;
        margin-right: 0;
      &.relate,&.page{
        background: #F9F9F9;
        color: #999999;
      }
    }
                }
              }
        }
        .successWayContent {
          padding: 38px 21px 31px;
          display: flex;
          justify-content: space-between;
          p {
            color: #3b7cff;
            cursor: pointer;
            text-decoration: underline;
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
    .bottom-right {
      width: 28%;
      box-sizing: border-box;
      max-width: 312px;
      padding:80px 16px 0 21px;
      position: relative;
      border-left: 1px dashed #EEEEEE;
          overflow: auto;
      .operate{
font-size: 14px;
font-weight: 500;
color: #333333;
line-height: 36px;
width: 100%;
padding:0 16px;
height: 36px;
border-radius: 8px;
cursor: pointer;
&.activeTool{
background: #F9F9F9;
}
img{
  width: 8px;
}
      }
      .link-table {
        padding:11px 0;
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          li {
            font-size: 12px;
font-weight: 500;
color: #666666;
line-height: 33px;
            height: 33px;
            position: relative;
            cursor: pointer;
            padding-left: 30px;
            &:hover {
              &::before {
                content: "";
                background-image: url(../../assets/images/add.png);
                background-size: 100% 100%;
                position: absolute;
                top: 50%;
                right: 6px;
                transform: translateY(-50%);
                display: inline-block;
                width: 26px;
                height: 26px;
              }
            }
          }
        }
      }
    }
  }
.successWayDialog{
  .relationspan {
    font-size: 14px;
    font-weight: 500;
    color: #121a26;
    line-height: 22px;
    margin-bottom: 10px;
  }
}
  .conditionalJumpTable {
    margin:11px 0 0;
    & > div {
      .blank{
        flex: 1;
      }
        .choice {
          width: 38%;
        }
        .question {
          width: 49%;
          text-align: center;
        }
        &.title{
          height: 40px;
          font-size: 12px;
font-weight: 600;
color: #CCCCCC;
line-height: 40px;
          border-bottom: 1px dashed #E8E8E8;
          .choice{
          }
        }
        &.content{
          height: 44px;
          line-height: 44px;
          border-bottom: 1px dashed #E8E8E8;
          font-size: 15px;
font-weight: 600;
color: #666666;
          .blank {
            font-size: 12px;
font-weight: 600;
color: #CCCCCC;
          }
          .question {
            font-size: 14px;
          }
        }
      }
  }
  .unconditionalJump {
    margin-top: 53px;
    .unconditionalTip {
      font-size: 13px;
font-family: PingFangSC-Medium, PingFang SC;
font-weight: 500;
color: #333333;
line-height: 18px;
margin-right: 16px;
display: inline-block;
    }
    .el-select {
      width: 50%;
    }
  }
  .relatedDialog{

  }

}
</style>
