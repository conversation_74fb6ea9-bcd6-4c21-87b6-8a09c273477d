import request from '@/utils/request'

// Survey API endpoints

/**
 * Get survey list with pagination
 * @param {Object} query - Query parameters
 */
export function getSurveyList(query) {
  return request({
    url: '/api/Survey/GetSurveyList',
    method: 'get',
    params: query
  })
}

/**
 * Get survey detail by ID
 * @param {String} id - Survey ID
 */
export function getSurveyById(id) {
  return request({
    url: `/api/Survey/GetSurveyById/${id}`,
    method: 'get'
  })
}

/**
 * Create new survey
 * @param {Object} data - Survey data
 */
export function createSurvey(data) {
  return request({
    url: '/api/Survey/CreateSurvey',
    method: 'post',
    data
  })
}

/**
 * Update existing survey
 * @param {Object} data - Survey data
 */
export function updateSurvey(data) {
  return request({
    url: '/api/Survey/UpdateSurvey',
    method: 'put',
    data
  })
}

/**
 * Delete survey
 * @param {String} id - Survey ID
 */
export function deleteSurvey(id) {
  return request({
    url: `/api/Survey/DeleteSurvey/${id}`,
    method: 'delete'
  })
}

/**
 * Get survey responses
 * @param {String} id - Survey ID
 * @param {Object} query - Query parameters
 */
export function getSurveyResponses(id, query) {
  return request({
    url: `/api/Survey/GetSurveyResponses/${id}`,
    method: 'get',
    params: query
  })
}

/**
 * Get survey statistics
 * @param {String} id - Survey ID
 */
export function getSurveyStatistics(id) {
  return request({
    url: `/api/Survey/GetSurveyStatistics/${id}`,
    method: 'get'
  })
}

/**
 * Associate survey with activity
 * @param {Object} data - Association data
 */
export function associateSurveyWithActivity(data) {
  return request({
    url: '/api/Survey/AssociateSurveyWithActivity',
    method: 'post',
    data
  })
}

/**
 * Remove survey from activity
 * @param {Object} data - Association data
 */
export function removeSurveyFromActivity(data) {
  return request({
    url: '/api/Survey/RemoveSurveyFromActivity',
    method: 'post',
    data
  })
}

/**
 * Get surveys associated with an activity
 * @param {String} activityId - Activity ID
 */
export function getSurveysByActivityId(activityId) {
  return request({
    url: `/api/Survey/GetSurveysByActivityId/${activityId}`,
    method: 'get'
  })
}
