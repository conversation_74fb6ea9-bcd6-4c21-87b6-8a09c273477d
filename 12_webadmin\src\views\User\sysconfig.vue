<template>
  <div class="updatepwd">
    <el-form ref="postForm" label-position="top" :rules="rules" :model="postForm" class="form">
      <el-form-item label="系统标题" prop="title">
        <el-input v-model.trim="postForm.title" type="text" placeholder="" />
      </el-form-item>
      <el-form-item label="系统logo" prop="logo">
        <el-upload
          class="avatar-uploader"
          :action="domainName + '/api/Upload/Post'"
          :show-file-list="false"
          :on-success="handleHeaderAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img v-if="postForm.logo" :src="postForm.logo" class="avatar" />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>

      <el-form-item class="btn">
        <el-button :loading="loading" type="primary" @click="submitForm">确认</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
const domainName = process.env.VUE_APP_BASE_API
import { updatePwd } from '@/api/auth'
import { fetchList, updateModel } from '@/apis/Admin/SysConfig'
export default {
  name: 'TUsersupdatepwd',
  components: {},
  data() {
    return {
      domainName: process.env.VUE_APP_BASE_API,
      rules: {
        title: [{ required: true, message: '系统标题', trigger: 'blur' }],
        logo: [{ required: true, message: '系统logo', trigger: 'blur' }]
      },
      postForm: {
        title: null,
        logo: null
      },
      list: [],
      loading: false
    }
  },
  computed: {},
  created() {
    fetchList({}).then(response => {
      this.list = response.rows
      for (const key in response.rows) {
        const item = response.rows[key]
        this.postForm[item.res_name] = item.res_values
      }
    })
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    handleAvatarSuccess(res, file) {
      if (res.state == 1) {
        this.postForm.logo = domainName + res.rows[0]
      } else {
        this.$message.error('上传失败')
      }
    },
    handleHeaderAvatarSuccess(res, file) {
      if (res.state == 1) {
        this.postForm.logo = domainName + res.rows[0]
      } else {
        this.$message.error('上传失败')
      }
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 2

      // if (!isJPG) {
      // this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      // return isJPG && isLt2M;
      return isLt2M
    },
    // 提交：新增/修改
    submitForm() {
      this.$refs['postForm'].validate(valid => {
        if (valid) {
          console.log(this.$store.state.user)
          const dataParam = this.list
          for (const key in dataParam) {
            const item = dataParam[key]
            item.res_values = this.postForm[item.res_name]
          }

          this.$confirm('确认要修改吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 获取到Token
            this.loading = true
            updateModel(dataParam)
              .then(response => {
                if (response.state === 1) {
                  this.$notify({
                    title: '成功',
                    message: '修改成功',
                    type: 'success',
                    duration: 2000
                  })
                  // this.resetForm('postForm')
                } else {
                  this.$notify({
                    title: '操作失败',
                    message: response.msg,
                    type: 'error',
                    duration: 2000
                  })
                }
                this.loading = false
              })
              .catch(err => {
                this.loading = false
                this.$notify({
                  title: '操作失败',
                  message: err,
                  type: 'error',
                  duration: 2000
                })
              })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.updatepwd {
  position: relative;
  margin: 14px 30px;
  background: #ffffff;
  border-radius: 6px;
  min-height: calc(100vh - 158px);
  .form {
    width: 334px;
    margin: auto;
    padding-top: 60px;
    .el-form-item {
      margin-bottom: 32px;
    }
    .btn {
      text-align: center;
      margin-top: 56px;
      button {
        width: 110px;
        height: 44px;
        border-radius: 8px;
        font-weight: 600;
      }
    }
  }
}
</style>
