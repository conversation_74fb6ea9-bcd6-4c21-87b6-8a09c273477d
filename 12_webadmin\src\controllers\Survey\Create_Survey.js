import { createSurvey, update<PERSON>urvey, getSurveyById } from '@/api/survey'

export default {
  name: 'Create<PERSON>ur<PERSON>',
  data() {
    return {
      surveyForm: {
        Id: '',
        SurveyName: '',
        SurveyDescription: '',
        Status: 0, // 0: draft, 1: published
        Questions: []
      },
      rules: {
        SurveyName: [
          { required: true, message: '请输入问卷标题', trigger: 'blur' },
          { max: 100, message: '问卷标题不能超过100个字符', trigger: 'blur' }
        ],
        SurveyDescription: [
          { max: 500, message: '问卷描述不能超过500个字符', trigger: 'blur' }
        ]
      },
      activeNames: [0],
      isEdit: false,
      submitting: false,
      questionTypeMap: {
        'text': '单行文本',
        'textarea': '多行文本',
        'radio': '单选题',
        'checkbox': '多选题',
        'select': '下拉选择',
        'date': '日期选择',
        'rate': '评分'
      }
    }
  },
  created() {
    // Check if we're editing an existing survey
    const surveyId = this.$route.query.id
    if (surveyId) {
      this.isEdit = true
      this.fetchSurveyData(surveyId)
    } else {
      // Add an initial question for new surveys
      this.addNewQuestion()
    }
  },
  methods: {
    fetchSurveyData(id) {
      getSurveyById(id).then(response => {
        const surveyData = response.Data
        this.surveyForm = {
          Id: surveyData.Id,
          SurveyName: surveyData.SurveyName,
          SurveyDescription: surveyData.SurveyDescription,
          Status: surveyData.Status,
          Questions: surveyData.Questions || []
        }
        
        // If no questions exist, add an initial one
        if (this.surveyForm.Questions.length === 0) {
          this.addNewQuestion()
        } else {
          // Ensure all questions have the necessary properties
          this.surveyForm.Questions.forEach((question, index) => {
            this.ensureQuestionProperties(question)
            // Make the first question active in the collapse
            if (index === 0) {
              this.activeNames = [0]
            }
          })
        }
      }).catch(() => {
        this.$message.error('获取问卷数据失败')
        this.goBack()
      })
    },
    goBack() {
      this.$router.push({ path: '/Survey/SurveyList' })
    },
    createDefaultQuestion() {
      return {
        QuestionText: '',
        QuestionType: 'radio',
        Required: false,
        Options: [
          { OptionText: '选项1', OptionValue: '1' },
          { OptionText: '选项2', OptionValue: '2' }
        ],
        RateMax: 5 // Default for rate questions
      }
    },
    ensureQuestionProperties(question) {
      // Ensure question has all required properties
      const defaultQuestion = this.createDefaultQuestion()
      
      // Set default options for choice-type questions if missing
      if (['radio', 'checkbox', 'select'].includes(question.QuestionType)) {
        if (!question.Options || question.Options.length === 0) {
          question.Options = [...defaultQuestion.Options]
        }
      }
      
      // Set default rate max if missing
      if (question.QuestionType === 'rate' && !question.RateMax) {
        question.RateMax = defaultQuestion.RateMax
      }
      
      // Set required flag if missing
      if (question.Required === undefined) {
        question.Required = defaultQuestion.Required
      }
    },
    addNewQuestion() {
      // Create a new default question
      const newQuestion = this.createDefaultQuestion()
      
      // Add to the list
      this.surveyForm.Questions.push(newQuestion)
      
      // Open the newly added question in the collapse
      this.$nextTick(() => {
        this.activeNames = [this.surveyForm.Questions.length - 1]
      })
    },
    removeQuestion(index) {
      if (this.surveyForm.Questions.length > 1) {
        this.surveyForm.Questions.splice(index, 1)
      }
    },
    duplicateQuestion(index) {
      const questionToDuplicate = this.surveyForm.Questions[index]
      const duplicatedQuestion = JSON.parse(JSON.stringify(questionToDuplicate))
      
      // Insert after the current question
      this.surveyForm.Questions.splice(index + 1, 0, duplicatedQuestion)
      
      // Open the newly duplicated question
      this.$nextTick(() => {
        this.activeNames = [index + 1]
      })
    },
    handleQuestionTypeChange(type, index) {
      const question = this.surveyForm.Questions[index]
      
      // Reset options if changing to or from a choice-type question
      if (['radio', 'checkbox', 'select'].includes(type)) {
        if (!question.Options || question.Options.length === 0) {
          question.Options = [
            { OptionText: '选项1', OptionValue: '1' },
            { OptionText: '选项2', OptionValue: '2' }
          ]
        }
      }
      
      // Set default rate max for rate questions
      if (type === 'rate' && !question.RateMax) {
        question.RateMax = 5
      }
    },
    addOption(questionIndex) {
      const question = this.surveyForm.Questions[questionIndex]
      const newOptionNumber = question.Options.length + 1
      question.Options.push({
        OptionText: `选项${newOptionNumber}`,
        OptionValue: `${newOptionNumber}`
      })
    },
    removeOption(questionIndex, optionIndex) {
      const question = this.surveyForm.Questions[questionIndex]
      if (question.Options.length > 2) {
        question.Options.splice(optionIndex, 1)
      }
    },
    getQuestionTypeLabel(type) {
      return this.questionTypeMap[type] || type
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.submitting = true
          
          // Check all questions have text
          let allValid = true
          this.surveyForm.Questions.forEach((question, index) => {
            if (!question.QuestionText) {
              allValid = false
              this.$message.error(`题目 ${index + 1} 内容不能为空`)
              this.activeNames = [index] // Open the invalid question
            }
            
            // Check options for choice questions
            if (['radio', 'checkbox', 'select'].includes(question.QuestionType)) {
              question.Options.forEach((option, optIndex) => {
                if (!option.OptionText) {
                  allValid = false
                  this.$message.error(`题目 ${index + 1} 选项 ${optIndex + 1} 内容不能为空`)
                  this.activeNames = [index] // Open the invalid question
                }
              })
            }
          })
          
          if (!allValid) {
            this.submitting = false
            return
          }
          
          const savePromise = this.isEdit
            ? updateSurvey(this.surveyForm)
            : createSurvey(this.surveyForm)
          
          savePromise.then(() => {
            this.$message({
              type: 'success',
              message: this.isEdit ? '问卷更新成功' : '问卷创建成功'
            })
            this.goBack()
          }).catch(() => {
            this.$message.error(this.isEdit ? '更新问卷失败' : '创建问卷失败')
          }).finally(() => {
            this.submitting = false
          })
        } else {
          return false
        }
      })
    }
  }
}
