import { getSurveyList, deleteSurvey, getSurveyById } from '@/api/survey'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'SurveyList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      isSearching: false,
      previewVisible: false,
      currentSurvey: null,
      listQuery: {
        PageIndex: 1,
        PageSize: 10,
        SurveyNameLike: ''
      },
      questionTypeMap: {
        'text': '文本输入',
        'textarea': '多行文本',
        'radio': '单选题',
        'checkbox': '多选题',
        'select': '下拉选择',
        'date': '日期选择',
        'rate': '评分'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getSurveyList(this.listQuery).then(response => {
        this.list = response.Data.Items
        this.total = response.Data.TotalCount
        this.listLoading = false
        this.isSearching = false
      }).catch(() => {
        this.listLoading = false
        this.isSearching = false
      })
    },
    handleFilter() {
      this.isSearching = true
      this.listQuery.PageIndex = 1
      this.getList()
    },
    formatDate(timestamp) {
      if (!timestamp) {
        return ''
      }
      return parseTime(timestamp, '{y}-{m}-{d} {h}:{i}')
    },
    handleAdd() {
      this.$router.push({ path: '/Survey/CreateSurvey' })
    },
    handleEdit(row) {
      this.$router.push({ path: `/Survey/CreateSurvey?id=${row.Id}` })
    },
    handleDelete(row) {
      this.$confirm('确认删除该问卷吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSurvey(row.Id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    viewResponses(row) {
      this.$router.push({ path: `/Survey/SurveyDetail/${row.Id}` })
    },
    viewAnalysis(row) {
      this.$router.push({ path: `/Survey/SurveyAnalysis/${row.Id}` })
    },
    previewSurvey(row) {
      this.currentSurvey = null
      this.previewVisible = true
      
      // Fetch full survey details including questions
      getSurveyById(row.Id).then(response => {
        this.currentSurvey = response.Data
      }).catch(() => {
        this.$message.error('获取问卷详情失败')
        this.previewVisible = false
      })
    },
    publishSurvey(row) {
      this.$confirm('确认发布该问卷吗？发布后用户可立即参与', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        // Call API to publish survey (update status to 1)
        const data = { Id: row.Id, Status: 1 }
        this.updateSurveyStatus(data, '发布')
      }).catch(() => {})
    },
    unpublishSurvey(row) {
      this.$confirm('确认下线该问卷吗？下线后用户将无法参与', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // Call API to unpublish survey (update status to 0)
        const data = { Id: row.Id, Status: 0 }
        this.updateSurveyStatus(data, '下线')
      }).catch(() => {})
    },
    updateSurveyStatus(data, actionName) {
      // In a real implementation, this would use a dedicated API method
      // For now, we'll simulate it with existing methods
      this.listLoading = true
      // Mock API call - in actual implementation use proper API
      setTimeout(() => {
        // Find and update status in local list
        const index = this.list.findIndex(item => item.Id === data.Id)
        if (index !== -1) {
          this.list[index].Status = data.Status
        }
        
        this.listLoading = false
        this.$message({
          type: 'success',
          message: `问卷${actionName}成功！`
        })
      }, 500)
    },
    getQuestionTypeLabel(type) {
      return this.questionTypeMap[type] || type
    }
  }
}
