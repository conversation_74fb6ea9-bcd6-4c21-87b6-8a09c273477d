<!--问卷分析页面-->
<template>
  <div class="analysis-container">
    <div class="header-section">
      <div class="header-content">
        <div class="back-button">
          <el-button type="text" @click="goBack">
            <i class="el-icon-arrow-left"></i> 返回问卷列表
          </el-button>
        </div>
        <div class="title-section">
          <h2>{{ survey.SurveyName || '问卷分析' }}</h2>
          <div class="survey-meta">
            <span class="meta-item">
              <i class="el-icon-time"></i>
              创建时间：{{ formatDate(survey.AddTime) }}
            </span>
            <span class="meta-item">
              <i class="el-icon-refresh"></i>
              更新时间：{{ formatDate(survey.UpdateTime) }}
            </span>
            <span class="meta-item">
              <i class="el-icon-s-data"></i>
              总回复数：{{ totalResponses }}
            </span>
            <el-tag :type="survey.Status === 1 ? 'success' : 'info'" effect="plain">
              {{ survey.Status === 1 ? '发布中' : '草稿' }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
    
    <div class="main-content" v-loading="loading">
      <el-card class="overview-card">
        <div slot="header">
          <span>问卷总览</span>
        </div>
        <div class="overview-stats">
          <div class="stat-card">
            <div class="stat-value">{{ totalResponses }}</div>
            <div class="stat-label">总回复数</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ completionRate }}%</div>
            <div class="stat-label">完成率</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ averageTime }}</div>
            <div class="stat-label">平均完成时间</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ totalQuestions }}</div>
            <div class="stat-label">题目数量</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="response-trend-card">
        <div slot="header">
          <span>回复趋势</span>
          <div class="header-actions">
            <el-radio-group v-model="timeRange" size="mini" @change="updateCharts">
              <el-radio-button label="week">最近7天</el-radio-button>
              <el-radio-button label="month">最近30天</el-radio-button>
              <el-radio-button label="all">全部时间</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-container">
          <div ref="responseTrendChart" class="chart"></div>
        </div>
      </el-card>
      
      <div v-if="survey.Questions && survey.Questions.length > 0" class="question-analysis-section">
        <h3 class="section-title">题目分析</h3>
        
        <el-card v-for="(question, index) in survey.Questions" :key="index" class="question-card">
          <div class="question-header">
            <span class="question-number">Q{{ index + 1 }}</span>
            <span class="question-text">{{ question.QuestionText }}</span>
            <span class="question-meta">
              <el-tag size="mini" effect="plain">{{ getQuestionTypeLabel(question.QuestionType) }}</el-tag>
              <el-tag size="mini" effect="plain" v-if="question.Required" type="danger">必填</el-tag>
            </span>
          </div>
          
          <!-- 单选题/多选题/下拉选择统计图表 -->
          <div v-if="['radio', 'checkbox', 'select'].includes(question.QuestionType)" class="question-chart-container">
            <div class="chart-wrapper">
              <div :ref="`questionChart_${index}`" class="chart"></div>
            </div>
            <div class="options-stats">
              <div v-for="(stat, optIndex) in getQuestionStats(question, index)" :key="optIndex" class="option-stat-item">
                <div class="option-text">{{ stat.label }}</div>
                <div class="option-value">
                  <div class="progress-bar">
                    <div class="progress" :style="{ width: stat.percentage + '%' }"></div>
                  </div>
                  <div class="stat-info">
                    <span>{{ stat.count }} 人</span>
                    <span>{{ stat.percentage }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 文本题/多行文本题回复 -->
          <div v-else-if="['text', 'textarea'].includes(question.QuestionType)" class="text-responses">
            <el-table
              :data="getTextResponses(index)"
              style="width: 100%">
              <el-table-column prop="user" label="用户" width="120"></el-table-column>
              <el-table-column prop="time" label="回复时间" width="180"></el-table-column>
              <el-table-column prop="response" label="回复内容"></el-table-column>
            </el-table>
            
            <div class="word-cloud-container">
              <div :ref="`wordCloud_${index}`" class="word-cloud"></div>
            </div>
          </div>
          
          <!-- 评分题统计 -->
          <div v-else-if="question.QuestionType === 'rate'" class="rate-stats">
            <div class="average-rate">
              <span>平均评分</span>
              <div class="rate-value">{{ getRateAverage(index) }}</div>
              <el-rate
                v-model="questionStats[index].averageRate"
                disabled
                show-score
                :max="question.RateMax || 5"
                text-color="#ff9900">
              </el-rate>
            </div>
            <div class="rate-distribution">
              <div :ref="`rateChart_${index}`" class="rate-chart"></div>
            </div>
          </div>
          
          <!-- 日期题统计 -->
          <div v-else-if="question.QuestionType === 'date'" class="date-stats">
            <div :ref="`dateChart_${index}`" class="date-chart"></div>
          </div>
        </el-card>
      </div>
      
      <el-card class="export-card">
        <div class="export-actions">
          <el-button type="primary" @click="exportToExcel">
            <i class="el-icon-download"></i> 导出问卷数据
          </el-button>
          <el-button type="info" @click="printReport">
            <i class="el-icon-printer"></i> 打印报告
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import controller from '@/controllers/Survey/Survey_Analysis'
export default controller
</script>

<style lang="scss" scoped>
.analysis-container {
  padding: 0 20px 20px;
  
  .header-section {
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .header-content {
      .back-button {
        margin-bottom: 15px;
      }
      
      .title-section {
        h2 {
          margin: 0 0 15px 0;
          font-size: 20px;
          color: #303133;
        }
        
        .survey-meta {
          color: #606266;
          font-size: 14px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 20px;
          
          .meta-item {
            display: inline-flex;
            align-items: center;
            
            i {
              margin-right: 5px;
            }
          }
          
          .el-tag {
            margin-left: 10px;
          }
        }
      }
    }
  }
  
  .main-content {
    .overview-card {
      margin-bottom: 20px;
      
      .overview-stats {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        
        .stat-card {
          text-align: center;
          padding: 15px;
          min-width: 120px;
          
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 5px;
          }
          
          .stat-label {
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
    
    .response-trend-card {
      margin-bottom: 20px;
      
      .header-actions {
        float: right;
      }
      
      .chart-container {
        height: 300px;
        
        .chart {
          height: 100%;
          width: 100%;
        }
      }
    }
    
    .question-analysis-section {
      .section-title {
        font-size: 18px;
        font-weight: bold;
        margin: 20px 0;
        color: #303133;
      }
      
      .question-card {
        margin-bottom: 20px;
        
        .question-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          
          .question-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background-color: #409EFF;
            color: white;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
          }
          
          .question-text {
            flex: 1;
            font-size: 16px;
            font-weight: bold;
            color: #303133;
          }
          
          .question-meta {
            margin-left: 15px;
            
            .el-tag {
              margin-left: 5px;
            }
          }
        }
        
        .question-chart-container {
          display: flex;
          margin-top: 20px;
          
          .chart-wrapper {
            flex: 0 0 45%;
            
            .chart {
              height: 300px;
              width: 100%;
            }
          }
          
          .options-stats {
            flex: 1;
            padding-left: 20px;
            
            .option-stat-item {
              margin-bottom: 15px;
              
              .option-text {
                margin-bottom: 5px;
                font-size: 14px;
                color: #606266;
              }
              
              .option-value {
                display: flex;
                flex-direction: column;
                
                .progress-bar {
                  height: 20px;
                  background-color: #f5f7fa;
                  border-radius: 4px;
                  overflow: hidden;
                  
                  .progress {
                    height: 100%;
                    background-color: #409EFF;
                  }
                }
                
                .stat-info {
                  display: flex;
                  justify-content: space-between;
                  margin-top: 5px;
                  font-size: 12px;
                  color: #909399;
                }
              }
            }
          }
        }
        
        .text-responses {
          margin-top: 20px;
          
          .word-cloud-container {
            height: 300px;
            margin-top: 20px;
            
            .word-cloud {
              height: 100%;
              width: 100%;
            }
          }
        }
        
        .rate-stats {
          display: flex;
          margin-top: 20px;
          
          .average-rate {
            flex: 0 0 250px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            
            span {
              margin-bottom: 10px;
              color: #606266;
            }
            
            .rate-value {
              font-size: 36px;
              font-weight: bold;
              color: #ff9900;
              margin-bottom: 10px;
            }
          }
          
          .rate-distribution {
            flex: 1;
            
            .rate-chart {
              height: 250px;
              width: 100%;
            }
          }
        }
        
        .date-stats {
          margin-top: 20px;
          
          .date-chart {
            height: 300px;
            width: 100%;
          }
        }
      }
    }
    
    .export-card {
      .export-actions {
        display: flex;
        justify-content: center;
        gap: 20px;
        padding: 10px;
      }
    }
  }
}
</style>
